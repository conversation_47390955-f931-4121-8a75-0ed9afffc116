import 'package:gp_stock_app/config/flavors/app_config.dart';

class F {
  static late final Flavor appFlavor;

  static String get name => appFlavor.name;

  static String get title {
    switch (appFlavor) {
      case Flavor.pre:
        return 'GP Pre';
      case Flavor.gp:
        return 'GP Stock';
      case Flavor.rsyp:
        return '荣顺优配';
      case Flavor.yhxt:
        return '沅和信投';
      case Flavor.goldenA:
        return 'GoldenA';
    }
  }
}
