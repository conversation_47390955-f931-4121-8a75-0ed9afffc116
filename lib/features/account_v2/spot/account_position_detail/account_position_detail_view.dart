import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/core/models/entities/account_info_entity.dart';
import 'package:gp_stock_app/core/theme/custom_text_theme.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';
import 'package:gp_stock_app/shared/theme/color_pallette.dart';
import 'package:gp_stock_app/shared/theme/font_pallette.dart';
import 'package:gp_stock_app/shared/theme/my_color_scheme.dart';
import 'package:gp_stock_app/shared/widgets/shimmer/shimmer_widget.dart';

import 'account_position_detail_cubit.dart';
import 'account_position_detail_state.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';

/// 持仓详情
class AccountPositionDetailView extends StatelessWidget {
  const AccountPositionDetailView({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AccountPositionDetailCubit, AccountPositionDetailState>(
      builder: (context, state) {
        // Only show shimmer during initial loading, not during polling updates
        if (state.model == null && (state.dataStatus == DataStatus.idle || state.dataStatus == DataStatus.loading)) {
          return _buildShimmerLayout(context);
        }
        if (state.model == null && state.dataStatus == DataStatus.failed) {
          return Container(color: Colors.redAccent);
        }
        final model = state.model!;

        // orderNumber 订单号
        // orderType 订单类型
        // transaction_price 成交价格
        // position_quantity 持仓数量
        // availableToSell 可卖
        // transaction_amount 成交金额
        // service_charge 手续费
        // holding_days 持有天数
        // margin_ratio 保证金比例
        // margin_amount 保证金金额
        // margin_alert_level 保证金预警线
        // forced_liquidation_line 保证金强平线
        // marginCall 追加保证金
        // take_profit_price 止盈价格
        // stop_loss_price 止损价格
        // distanceToWarningLine 距离预警线
        // distanceToLiquidationLine 距离强平线
        final orderNumberRow = _buildInfoRowEx('orderNumber', model.orderNo); // 订单号
        final orderTypeRow = _buildInfoRowEx('orderType', model.tradeType == 1 ? 'openLongSymbol'.tr() : 'openShortSymbol'.tr(), contentColor: Color(0xffF5222D)); // 订单类型
        final transactionPriceRow = _buildInfoRowEx('transaction_price', model.costPrice); // 成交价格
        final positionQuantityRow = _buildInfoRowEx('position_quantity', model.positionTotalNum); // 持仓数量
        final availableToSellRow = _buildInfoRowEx('availableToSell', model.restNum); // 可卖
        final serviceChargeRow = _buildInfoRowEx('service_charge', model.feeAmount); // 手续费
        final holdingDaysRow = _buildInfoRowEx('holding_days', model.positionDays); // 持有天数
        final marginRatioRow = _buildInfoRowEx('margin_ratio', model.marginRatio, isPercent: true); // 保证金比例
        final marginAmountRow = _buildInfoRowEx('margin_amount', model.marginAmount); // 保证金金额
        final marginAlertLevelRow = _buildInfoRowEx('margin_alert_level', model.warningLine, contentColor: Color(0xffF5222D)); // 保证金预警线
        final forcedLiquidationLineRow =
            _buildInfoRowEx('forced_liquidation_line', model.closeLine); // 保证金强平线
        final marginCallRow = _buildInfoRowEx('marginCall', model.appendMargin); // 追加保证金
        final takeProfitPriceRow = _buildInfoRowEx('take_profit_price', model.takeProfitValue); // 止盈价格
        final stopLossPriceRow = _buildInfoRowEx('stop_loss_price', model.stopLossValue); // 止损价格
        final distanceToWarningLineRow = _buildInfoRowEx('distanceToWarningLine', model.distanceCloseLine); // 距离预警线
        final distanceToLiquidationLineRow =
            _buildInfoRowEx('distanceToLiquidationLine', model.distanceWarningLine); // 距离强平线
        //
        // // 体验和彩金 _model.type == 2 || _model.type == 3
        // var renewalStatusRow = _buildInfoRowEx('contractRenewalStatus', "maturitySettlement".tr());
        // if (model.type == 1) {
        //   // 普通合约 general contract
        //   renewalStatusRow = _buildInfoRowEx(
        //     'contractRenewalStatus',
        //     model.isAutoRenew ? "autoRenewal".tr() : "maturitySettlement".tr(),
        //   );
        // }
        // final giftAmountRow = _buildInfoRowEx('giftAmount', model.giveAmount); // 彩金金额
        // final contractNetAssetsRow = _buildInfoRowEx('contractNetAssets', model.contractAssetAmount); // 合约净资产
        //
        List<Widget> rows = [
            orderNumberRow, // 订单号
            orderTypeRow, // 起始保证金
            transactionPriceRow, // 亏损警戒线
            positionQuantityRow, // 亏损平仓线
            availableToSellRow, // 利率利息
            serviceChargeRow, // 市值
            holdingDaysRow, // 扩大保证金
            marginRatioRow, // 追加保证金
            marginAmountRow, // 浮动盈亏
            marginAlertLevelRow, // 亏损率
            forcedLiquidationLineRow, // 提现金额
            marginCallRow, // 冻结金额
            takeProfitPriceRow, // 止盈价格
            stopLossPriceRow, // 止损价格
            distanceToWarningLineRow, // 距离预警线金额
            distanceToLiquidationLineRow, // 距离强平线金额
          ];


        return _buildContentLayout(context, rows, model);
      },
    );
  }

  Widget _buildContentLayout(BuildContext context, List<Widget> rows, PositionEntity model) {
    return Scaffold(
      appBar: AppBar(
        surfaceTintColor: Colors.transparent,
        backgroundColor: context.theme.cardColor,
        title: Text('trade_detail_page'.tr()),
      ),
      body: SingleChildScrollView(
        child: Column(
          children: [
            _buildSummaryHeaderCard(model),
            Container(
              margin: EdgeInsets.symmetric(horizontal: 14.gr),
              decoration: BoxDecoration(
                color: context.theme.cardColor,
                borderRadius: BorderRadius.circular(12.gr),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.1),
                    blurRadius: 10,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: Padding(
                padding: EdgeInsets.all(16.gr),
                child: Column(
                  children: rows,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

// Shimmer layout for loading state
  Widget _buildShimmerLayout(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        surfaceTintColor: Colors.transparent,
        backgroundColor: context.theme.cardColor,
        title: Text('contractDetails'.tr()),
      ),
      body: SingleChildScrollView(
        child: Column(
          children: [
            Container(
              margin: EdgeInsets.all(16.gr),
              decoration: BoxDecoration(
                color: context.theme.cardColor,
                borderRadius: BorderRadius.circular(12.gr),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.1),
                    blurRadius: 10,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: Padding(
                padding: EdgeInsets.all(16.gr),
                child: Column(
                  children: List.generate(4, (index) => _buildShimmerRow(context)),
                ),
              ),
            ),
            Container(
              margin: EdgeInsets.all(16.gr),
              decoration: BoxDecoration(
                color: context.theme.cardColor,
                borderRadius: BorderRadius.circular(12.gr),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.1),
                    blurRadius: 10,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: Padding(
                padding: EdgeInsets.all(16.gr),
                child: Column(
                  children: List.generate(12, (index) => _buildShimmerRow(context)),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildShimmerRow(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 4.gh),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          ShimmerWidget(
            height: 16.gh,
            width: 100.gw,
            radius: 4.gr,
            color: context.theme.scaffoldBackgroundColor,
          ),
          ShimmerWidget(
            height: 16.gh,
            width: 80.gw,
            radius: 4.gr,
            color: context.theme.scaffoldBackgroundColor,
          ),
        ],
      ),
    );
  }

  Widget _buildInfoRowEx(String labelKey, dynamic value, {bool isPercent = false, Color? contentColor}) {
    String textValue;

    if (value is double) {
      textValue = value.toStringAsFixed(2);
      if (isPercent) textValue += '%';
    } else if (value != null) {
      textValue = value.toString();
    } else {
      textValue = '0.00';
    }

    return Padding(
      padding: EdgeInsets.symmetric(vertical: 4.gh),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            labelKey.tr(),
            style: FontPalette.normal14
          ),
          Expanded(
            child: Text(
              textValue,
              style: FontPalette.bold14.copyWith(
                color: contentColor ?? ColorPalette.primaryColor,
                fontFamily: 'Akzidenz-Grotesk',
              ),
              maxLines: 2,
              textAlign: TextAlign.right,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryHeaderCard(PositionEntity model) {
    return Container(
      margin: EdgeInsets.symmetric(vertical: 12.gw, horizontal: 14.gw),
      padding: EdgeInsets.fromLTRB(14.gw, 12.gw, 14.gw, 26.gw),
      decoration: BoxDecoration(
        color: ColorPalette.whiteColor,
        borderRadius: BorderRadius.circular(8.gr),
        boxShadow: [
          BoxShadow(
            color: Color(0x14354677), // #35467714，8%
            offset: Offset(0, 4),
            blurRadius: 8,
            spreadRadius: 0,
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Text(model.symbolName, style: FontPalette.normal14.copyWith(color: ColorPalette.primaryColor),),
              11.horizontalSpace,
              Container(
                height: 15.gh,
                padding: EdgeInsets.symmetric(horizontal: 4.gh),
                color: Color(0xffE9F0FD),
                alignment: Alignment.center,
                child: Text(
                  model.market,
                  style: FontPalette.normal12.copyWith(color: ColorPalette.primaryColor),
                ),
              ),
              const Spacer(),

              Container(
                height: 28.gh,
                padding: EdgeInsets.symmetric(horizontal: 22.gh),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(6.gr),
                  color: model.type == 1 ? const Color(0xFFF5222D) // 红色
                      : ColorPalette.customColor,
                ),
                alignment: Alignment.center,
                child: Text(
                  TradeTypeOption.fromValue(model.tradeType).text,
                  style: FontPalette.normal12.copyWith(color: Colors.white),
                ),
              ),
            ],
          ),
          16.verticalSpace,
          Text("${'positionProfitLoss'.tr()}(${model.currency})", style: FontPalette.normal14.copyWith(color: ColorPalette.titleColor),),
          11.verticalSpace,

          Text((model.floatingProfitLoss).toStringAsFixed(2), style: FontPalette.normal24.copyWith(color: Color(0xffFF0000), fontFamily: 'Akzidenz-Grotesk'),),
          5.verticalSpace,
          Text("${(model.floatingProfitLossRate).toStringAsFixed(2)}%", style: FontPalette.normal14.copyWith(color: Color(0xffC92C31)),),
        ],
      ),
    );
  }
}
