import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/features/account_v2/0_home/logic/account_screen_state_v2.dart';
import 'package:gp_stock_app/features/account_v2/contract/account_contract_screen.dart' show AccountContractScreen;
import 'package:gp_stock_app/features/account_v2/spot/account_spot_screen.dart';
import 'package:gp_stock_app/shared/widgets/page_view/direct_slide_page_view.dart';
import 'package:gp_stock_app/shared/widgets/tab/custom_tab_bar.dart';

import 'logic/account_screen_cubit_v2.dart';

class AccountScreenV2 extends StatefulWidget {
  const AccountScreenV2({super.key});

  @override
  State<StatefulWidget> createState() => _AccountScreenV2State();
}

class _AccountScreenV2State extends State<AccountScreenV2> {
  final pageController = PageController();
  int currentIndex = 0;

  @override
  void initState() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<AccountScreenCubitV2>().fetchSpotScreenCurrentData();
    });
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final cubit = context.read<AccountScreenCubitV2>();
    return BlocBuilder<AccountScreenCubitV2, AccountScreenStateV2>(
      buildWhen: (previous, current) => previous.tradingTabBarCurrentIndex != current.tradingTabBarCurrentIndex,
      builder: (context, state) {
        return Column(
          children: [
            4.verticalSpace,
            CustomTabBar(
              tabs: state.tabBarList.map((e) => e.nameKey.tr()).toList(),
              selectedIndex: state.tradingTabBarCurrentIndex,
              onTabSelected: (index) => cubit.updateAccountTabBarIndex(index),
            ),
            Expanded(
              child: DirectSlideView(
                pageIndex: state.tradingTabBarCurrentIndex,
                physics: const NeverScrollableScrollPhysics(),
                pages: [
                  AccountSpotScreen(), // 现货
                  AccountContractScreen(), // 合约
                ],
              ),
            ),
          ],
        );
      },
    );
  }
}
