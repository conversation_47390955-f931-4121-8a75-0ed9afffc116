import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:gp_stock_app/core/theme/custom_text_theme.dart';
import 'package:gp_stock_app/features/chat/utils/enums.dart';
import 'package:gp_stock_app/features/chat/utils/theme.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';

class ChatTypeTabs extends StatelessWidget implements PreferredSizeWidget {
  const ChatTypeTabs({
    super.key,
    required this.index,
    this.onTap,
    required this.types,
    this.groupCount = 0,
    this.userCount = 0,
  });

  final int index;
  final ValueChanged<int>? onTap;
  final List<ChatScreenType> types;
  final int groupCount;
  final int userCount;

  @override
  Widget build(BuildContext context) {
    return TabBar(
      onTap: onTap,
      tabs: [
        if (types.contains(ChatScreenType.all))
          Tab(
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Text(
                  'all'.tr(),
                  style: TextStyle(fontSize: 14, fontWeight: FontWeight.w500),
                ),
              ],
            ),
          ),
        if (types.contains(ChatScreenType.channels))
          Tab(
            child: Row(
              spacing: 4,
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Text(
                  'chat_channels'.tr(),
                  style: TextStyle(fontSize: 14, fontWeight: FontWeight.w500),
                ),
                Container(
                  padding: const EdgeInsets.fromLTRB(6, 0, 6, 0),
                  decoration: BoxDecoration(
                    color: index == 1
                        ? context.theme.primaryColor
                        : context.isLightMode
                            ? Colors.black26
                            : Colors.grey.shade800,
                    borderRadius: BorderRadius.circular(6),
                  ),
                  child: Text(
                    groupCount.toString(),
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                    ),
                  ),
                ),
              ],
            ),
          ),
        if (types.contains(ChatScreenType.users))
          Tab(
            child: Row(
              spacing: 4,
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Text(
                  'chat_users'.tr(),
                  style: TextStyle(fontSize: 14, fontWeight: FontWeight.w500),
                ),
                Container(
                  padding: const EdgeInsets.fromLTRB(8, 1, 8, 1),
                  decoration: BoxDecoration(
                    color: index == 2
                        ? context.theme.primaryColor
                        : context.isLightMode
                            ? Colors.black26
                            : Colors.grey.shade800,
                    borderRadius: BorderRadius.circular(6),
                  ),
                  child: Text(
                    userCount.toString(),
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                    ),
                  ),
                ),
              ],
            ),
          ),
      ],
      dividerHeight: 0,
      indicatorSize: TabBarIndicatorSize.label,
      indicatorColor: context.theme.primaryColor,
      labelColor: context.theme.primaryColor,
      unselectedLabelColor: context.colorTheme.textPrimary,
      indicatorPadding: const EdgeInsets.only(bottom: 10, left: 30, right: 30),
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(48.0);
}
