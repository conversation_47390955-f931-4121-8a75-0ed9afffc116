import 'package:flutter/material.dart';
import 'package:gp_stock_app/core/theme/custom_text_theme.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/features/chat/screens/chat_screen.dart';
import 'package:gp_stock_app/features/chat/utils/utils.dart';
import 'package:gp_stock_app/features/chat/widgets/chat/chat_tile_heading.dart';
import 'package:gp_stock_app/features/chat/widgets/chat/chat_tile_sub.dart';
import 'package:gp_stock_app/features/chat/widgets/chat/dp.dart';
import 'package:gp_stock_app/features/chat/widgets/chat/support_dp.dart';
import 'package:tencent_cloud_chat_uikit/tencent_cloud_chat_uikit.dart';

import '../../utils/theme.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';

class ChatTile extends StatelessWidget {
  const ChatTile({
    super.key,
    required this.data,
  });

  final V2TimConversation data;

  @override
  Widget build(BuildContext context) {
    final freindInfo = data.friendInfo;
    final messagingFrequency = data.messagingFrequency;
    return InkWell(
      onTap: () {
        if (data.type == 1) {
          Navigator.push<dynamic>(
            context,
            MaterialPageRoute(
              builder: (context) => ChatScreen(
                selectedConversation: data,
                nickName: freindInfo?.userProfile?.nickName ?? data.showName,
                freindInfo: freindInfo,
              ),
            ),
          );
        }
        if (data.type == 2) {
          Navigator.push<dynamic>(
            context,
            MaterialPageRoute(
              builder: (context) => ChatScreen(
                selectedConversation: data,
                nickName: freindInfo?.userProfile?.nickName ?? data.showName,
                messagingFrequency: messagingFrequency,
              ),
            ),
          );
        }
      },
      child: Container(
        color: context.theme.scaffoldBackgroundColor,
        padding: const EdgeInsets.fromLTRB(16, 6, 16, 0),
        child: Column(
          children: [
            Row(
              spacing: 10,
              children: [
                if (freindInfo != null && (freindInfo.userProfile?.role == 1 || freindInfo.userProfile?.role == 2))
                  SupportDp(
                    faceUrl: data.faceUrl,
                    userId: data.userID ?? data.groupID,
                    name: freindInfo.userProfile?.nickName ?? data.showName,
                  )
                else
                  Dp(
                    faceUrl: data.faceUrl,
                    type: data.type,
                    userId: data.userID ?? data.groupID,
                    name: freindInfo?.userProfile?.nickName ?? data.showName,
                  ),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      ChatTileHeading(data: data),
                      ChatTileSub(data: data),
                    ],
                  ),
                ),
              ],
            ),
            Padding(
              padding: const EdgeInsets.only(top: 8),
              child: Divider(
                thickness: 0.35,
                height: 0,
                indent: 60,
                endIndent: 15,
                color: context.theme.dividerColor,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
