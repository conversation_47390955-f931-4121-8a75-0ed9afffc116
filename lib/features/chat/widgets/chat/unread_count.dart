import 'package:flutter/material.dart';
import 'package:gp_stock_app/core/theme/custom_text_theme.dart';
import 'package:gp_stock_app/features/chat/utils/theme.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';

class UnreadCount extends StatelessWidget {
  const UnreadCount({
    super.key,
    required this.unreadCount,
  });

  final String? unreadCount;

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        if (unreadCount != '0')
          CircleAvatar(
            backgroundColor: context.theme.primaryColor,
            radius: 10,
            child: Text(
              unreadCount!,
              style: TextStyle(
                fontSize: (unreadCount?.length ?? 0) > 2 ? 8 : 9,
                color: Colors.white,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
      ],
    );
  }
}
