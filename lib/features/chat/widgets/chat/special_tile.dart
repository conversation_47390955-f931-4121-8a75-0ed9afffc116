import 'package:flutter/material.dart';
import 'package:flutter_lucide/flutter_lucide.dart';
import 'package:gp_stock_app/core/theme/custom_text_theme.dart';
import 'package:gp_stock_app/features/chat/screens/chat_screen.dart';
import 'package:gp_stock_app/features/chat/utils/theme.dart';
import 'package:gp_stock_app/features/chat/utils/utils.dart';
import 'package:gp_stock_app/features/chat/widgets/chat/dp.dart';
import 'package:gp_stock_app/shared/theme/my_color_scheme.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';

class SpecialTile extends StatelessWidget {
  const SpecialTile({
    super.key,
    this.name,
    this.url,
    this.groupId,
    this.userId,
    this.messagingFrequency,
  });

  final String? name;
  final String? url;
  final String? groupId;
  final String? userId;
  final int? messagingFrequency;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {
        if (userId != null) {
          Navigator.push<dynamic>(
            context,
            MaterialPageRoute(
              builder: (context) => ChatScreen(
                selectedConversation: getConversation(
                  userID: userId,
                  name: name,
                ),
              ),
            ),
          );
        }
        if (groupId != null) {
          Navigator.push<dynamic>(
            context,
            MaterialPageRoute(
              builder: (context) => ChatScreen(
                selectedConversation: getConversation(
                  groupID: groupId,
                  name: name,
                ),
                messagingFrequency: messagingFrequency,
              ),
            ),
          );
        }
      },
      child: Container(
        padding: const EdgeInsets.fromLTRB(16, 6, 12, 6),
        child: Column(
          children: [
            Row(
              children: [
                Dp(
                  faceUrl: url,
                  type: groupId != null ? 2 : 1,
                  userId: userId ?? groupId,
                  name: name,
                ),
                Expanded(
                  child: Container(
                    alignment: Alignment.centerLeft,
                    padding: const EdgeInsets.only(right: 28, left: 12),
                    child: Text(
                      name ?? '',
                      style: TextStyle(
                        color: context.colorTheme.textPrimary,
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.only(right: 12),
                  child: Icon(
                    LucideIcons.send_horizontal,
                    color: context.theme.primaryColor.withValues(alpha: 0.5),
                    size: 18,
                  ),
                ),
              ],
            ),
            Padding(
              padding: const EdgeInsets.only(top: 5),
              child: Divider(
                thickness: 0.35,
                height: 0,
                indent: 60,
                endIndent: 15,
                color: context.theme.dividerColor,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
