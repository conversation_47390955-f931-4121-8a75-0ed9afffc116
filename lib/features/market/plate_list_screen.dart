import 'dart:developer';

import 'package:animated_flip_counter/animated_flip_counter.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/core/theme/custom_text_theme.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/market/domain/models/plate_response.dart';
import 'package:gp_stock_app/shared/app/extension/color_extension.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';
import 'package:gp_stock_app/shared/models/route_arguments/trading_arguments.dart';
import 'package:gp_stock_app/shared/routes/routes.dart';
import 'package:gp_stock_app/shared/widgets/symbol/symbol_chip.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

import '../../shared/models/stock/stock_response.dart';
import '../../shared/theme/font_pallette.dart';
import '../../shared/theme/my_color_scheme.dart';
import '../../shared/widgets/pagination/common_refresher.dart';
import '../../shared/widgets/shimmer/shimmer_widget.dart';
import '../../shared/widgets/sort_header.dart';
import 'logic/market/market_cubit.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';

class PlateListScreen extends StatefulWidget {
  const PlateListScreen({super.key});

  @override
  State<PlateListScreen> createState() => _PlateListScreenState();
}

class _PlateListScreenState extends State<PlateListScreen> {
  @override
  void initState() {
    super.initState();
    context.read<MarketCubit>().resetPlateData();
    context.read<MarketCubit>().fetchPlateList();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: context.theme.cardColor,
        surfaceTintColor: Colors.transparent,
        title: Text('industry_sectors'.tr()),
      ),
      body: _MarketDataTable(),
    );
  }
}

class _MarketDataTable extends StatefulWidget {
  const _MarketDataTable();

  @override
  State<_MarketDataTable> createState() => _MarketDataTableState();
}

class _MarketDataTableState extends State<_MarketDataTable> {
  final _controller = RefreshController();

  void _onRefresh(BuildContext context, PlateResponse? items) {
    context.read<MarketCubit>().fetchPlateList(isLoadMore: false);
    _controller.resetNoData();
    _controller.refreshCompleted();
  }

  void _onLoadMore(BuildContext context, PlateResponse? items, DataStatus status) {
    log(status.toString());

    if (status == DataStatus.success) {
      context.read<MarketCubit>().fetchPlateList(isLoadMore: true);
      _controller.loadComplete();
    }
  }

  @override
  Widget build(BuildContext context) {
    return _buildTableContainer();
  }

  Widget _buildTableContainer() {
    return Container(
      decoration: BoxDecoration(
        color: context.theme.cardColor,
        borderRadius: BorderRadius.circular(8.gr),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withNewOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Padding(
        padding: EdgeInsets.all(18.gr),
        child: Column(
          children: [
            _buildTableHeader(),
            10.verticalSpace,
            Divider(color: context.theme.dividerColor),
            _buildTableContent(),
          ],
        ),
      ),
    );
  }

  Widget _buildTableHeader() {
    return BlocBuilder<MarketCubit, MarketState>(
      builder: (context, state) {
        return Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Expanded(
              flex: 5,
              child: Text(
                'name'.tr(),
                style: _headerTextStyle,
              ),
            ),
            Expanded(
              flex: 6,
              child: Row(
                children: [
                  Expanded(
                    flex: 3,
                    child: SortHeader(
                      title: 'change'.tr(),
                      sortType: state.sortByChangePlateListAsc,
                      onTap: context.read<MarketCubit>().handleSortByChangePlateList,
                      textStyle: FontPalette.medium12,
                    ),
                  ),
                  Expanded(
                    flex: 2,
                    child: Text(
                      'leadingStocks'.tr(),
                      style: _headerTextStyle,
                    ),
                  ),
                ],
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildTableContent() {
    return BlocSelector<MarketCubit, MarketState, (DataStatus, PlateResponse?, DataStatus, int)>(
      selector: (state) => (
        state.plateFetchStatusA,
        state.plateResponseA,
        state.paginationStatus,
        state.pageNumber,
      ),
      builder: (context, data) {
        final (status, items, paginationStatus, pageNumber) = data;

        if (status == DataStatus.loading) {
          return _buildLoadingList();
        }

        if (items?.data?.records?.isEmpty ?? true) {
          return _buildEmptyState();
        }

        return SizedBox(
          height: .75.gsh,
          child: CommonRefresher(
            bgColor: context.theme.cardColor,
            controller: _controller,
            enablePullDown: true,
            enablePullUp: true,
            onRefresh: () => _onRefresh(context, items),
            onLoading: () => _onLoadMore(context, items, paginationStatus),
            child: ListView.builder(
              physics: const NeverScrollableScrollPhysics(),
              shrinkWrap: true,
              itemCount: items?.data?.records?.length ?? 0,
              itemBuilder: (context, index) => _MarketTableRow(data: items?.data?.records![index] ?? StockItem()),
            ),
          ),
        );
      },
    );
  }

  Widget _buildLoadingList() {
    return ListView.builder(
      itemCount: 10,
      shrinkWrap: true,
      physics: const AlwaysScrollableScrollPhysics(),
      itemBuilder: (context, index) => Padding(
        padding: EdgeInsets.only(bottom: 8.gh),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(8.gr),
          child: ShimmerWidget(
            height: 45.gh,
            width: double.infinity,
          ),
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Text(
        'no_data_available'.tr(),
        style: FontPalette.medium14.copyWith(
          color: Colors.black54,
        ),
      ),
    );
  }

  TextStyle get _headerTextStyle => FontPalette.medium12.copyWith(
        color: context.colorTheme.textRegular,
      );
}

class _MarketTableRow extends StatelessWidget {
  const _MarketTableRow({
    required this.data,
  });

  final StockItem data;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(
        horizontal: 4.gw,
      ),
      child: GestureDetector(
        onTap: () => Navigator.pushNamed(context, routeTradingCenter,
            arguments: TradingArguments(instrumentInfo: data.instrumentInfo, selectedIndex: 1)),
        child: Container(
          color: Colors.transparent,
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8.0),
            child: Row(
              spacing: 4.gh,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                // Name and Symbol Column
                Expanded(
                  flex: 4,
                  child: Text(
                    data.name ?? 'N/A',
                    style: FontPalette.normal14.copyWith(
                      color: context.colorTheme.textRegular,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),

                Expanded(
                    flex: 2,
                    child: Row(
                      children: [
                        AnimatedFlipCounter(
                          fractionDigits: 2,
                          prefix: (data.gain ?? 0.00) > 0 ? '+' : '',
                          suffix: '%',
                          decimalSeparator: '.',
                          thousandSeparator: ',',
                          textStyle: FontPalette.bold14.copyWith(
                            color: (data.gain ?? 0.00).getValueColor(context),
                            fontFamily: 'Akzidenz-Grotesk',
                          ),
                          value: (data.gain ?? 0.00) * 100,
                        ),
                      ],
                    )),

                Expanded(
                  flex: 3,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Text(
                        data.nameAlt ?? 'N/A',
                        textAlign: TextAlign.center,
                        style: FontPalette.bold12.copyWith(color: context.colorTheme.textRegular),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        spacing: 4.gh,
                        children: [
                          SymbolChip(
                            name: data.market ?? '',
                          ),
                          Text(
                            data.symbol ?? 'N/A',
                            textAlign: TextAlign.center,
                            style: FontPalette.semiBold12.copyWith(color: context.colorTheme.textRegular),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
