import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';

import '../../../../core/dependency_injection/injectable.dart';
import '../../../../shared/theme/color_pallette.dart';
import '../../../../shared/theme/my_color_scheme.dart';
import '../../logic/search/search_cubit.dart';
import '../../market_search_screen.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';

class Header extends StatelessWidget implements PreferredSizeWidget {
  const Header({
    super.key,
    this.showBackButton,
  });

  final bool? showBackButton;

  @override
  Widget build(BuildContext context) {
    return AppBar(
      backgroundColor: context.theme.cardColor,
      surfaceTintColor: Colors.transparent,
      elevation: 0,
      centerTitle: true,
      leading: showBackButton ?? false
          ? IconButton(
              icon: Icon(
                Icons.arrow_back_ios_outlined,
                color: ColorPalette.titleColor,
              ),
              onPressed: () {
                Navigator.pop(context);
              },
            )
          : const SizedBox(),
      actions: [
        IconButton(
          icon: Icon(
            Icons.search,
            color: ColorPalette.textColor3,
          ),
          onPressed: () {
            context.verifyAuth(
              () => Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => BlocProvider(
                    create: (context) => getIt<SearchCubit>(),
                    child: const MarketSearchScreen(),
                  ),
                ),
              ),
            );
          },
        ),
      ],
    );
  }

  @override
  Size get preferredSize => Size.fromHeight(60.gh);
}
