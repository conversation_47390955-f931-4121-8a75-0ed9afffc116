import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/market/domain/models/stock_reponse/stock_info_response.dart';
import 'package:gp_stock_app/features/market/logic/cubit/index_trade_cubit.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';
import 'package:gp_stock_app/shared/models/route_arguments/trading_arguments.dart';
import 'package:gp_stock_app/shared/routes/routes.dart';
import 'package:gp_stock_app/shared/theme/font_pallette.dart';
import 'package:gp_stock_app/shared/theme/my_color_scheme.dart';
import 'package:gp_stock_app/shared/widgets/symbol/symbol_chip.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';

class GraphCardSelection extends StatelessWidget {
  const GraphCardSelection({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<IndexTradeCubit, IndexTradeState>(
      builder: (context, state) {
        if (state.indexStocks.isEmpty) return SizedBox.shrink();
        return Padding(
          padding: EdgeInsets.only(left: 16.gh, top: 0.gh, right: 16.gh, bottom: 0.gh),
          child: Row(
            spacing: 8,
            children: [
              Expanded(
                flex: 1,
                child: Text(
                  state.indexStocks[state.selectedIndex].stockInfo.data?.name ?? '',
                  style: FontPalette.bold14,
                ),
              ),
              SymbolChip(
                name: state.indexStocks[state.selectedIndex].stockInfo.data?.market ?? '',
                chipColor: context.theme.primaryColor,
              ),
              Text(
                state.indexes[state.selectedIndex].symbol,
                style: FontPalette.bold14,
              ),
              Text(
                '${((state.indexStocks[state.selectedIndex].stockInfo.data?.gain ?? 0) * 100).toStringAsFixed(2)} %',
                style: FontPalette.bold14.copyWith(
                    fontFamily: 'Akzidenz-Grotesk',
                    color: state.indexStocks[state.selectedIndex].stockInfo.data?.gain?.getValueColor(context)),
              ),
              TextButton(
                onPressed: () {
                  final i = state.selectedIndex;
                  final data = state.indexStocks;
                  context.verifyAuth(
                    () {
                      StockInfoData? stockInfo = data[i].stockInfo.data;
                      Navigator.pushNamed(
                        context,
                        routeTradingCenter,
                        arguments: TradingArguments(
                          instrumentInfo: stockInfo!.instrumentInfo,
                          selectedIndex: TradeTabType.Quotes.index,
                          isIndexTrading: true,
                          shouldNavigateToIndex: true,
                        ),
                      );
                    },
                  );
                },
                style: TextButton.styleFrom(
                  foregroundColor: context.theme.primaryColor,
                  padding: EdgeInsets.symmetric(horizontal: 4.gh, vertical: 4.gh),
                  minimumSize: const Size(0, 0),
                  tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                ),
                child: Text("detail".tr()),
              ),
            ],
          ),
        );
      },
    );
  }
}
