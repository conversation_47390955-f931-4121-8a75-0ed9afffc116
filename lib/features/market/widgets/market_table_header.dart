import 'package:flutter/material.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:gp_stock_app/core/theme/custom_text_theme.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';

import '../../../shared/theme/font_pallette.dart';
import '../../../shared/theme/my_color_scheme.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';

class MarketTableHeader extends StatelessWidget {
  final String title;
  final bool isSelected;
  final VoidCallback onTap;
  final bool disableScaleAnimation;

  const MarketTableHeader({
    super.key,
    required this.title,
    required this.isSelected,
    required this.onTap,
    this.disableScaleAnimation = false,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        GestureDetector(
          onTap: onTap,
          child: disableScaleAnimation
              ? Text(
                  title,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                  style: FontPalette.normal14.copyWith(
                    color: isSelected ? context.theme.primaryColor: context.colorTheme.textPrimary,
                  ),
                )
              : AnimationConfiguration.synchronized(
                  duration: const Duration(milliseconds: 200),
                  child: ScaleAnimation(
                    scale: isSelected ? 1.05 : 1.0,
                    child: Text(
                      title,
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                      style: FontPalette.normal14.copyWith(
                        color: isSelected ? context.theme.primaryColor : context.colorTheme.textPrimary,
                      ),
                    ),
                  ),
                ),
        ),
        if (isSelected) ...[
          5.verticalSpace,
          AnimationConfiguration.synchronized(
            duration: const Duration(milliseconds: 300),
            child: FadeInAnimation(
              child: SlideAnimation(
                horizontalOffset: 20.0,
                child: Container(
                  width: 30.gw,
                  height: 2.gh,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(4.0),
                    color: context.theme.primaryColor,
                  ),
                ),
              ),
            ),
          ),
        ] else ...[
          5.verticalSpace,
          SizedBox(
            width: 30.gw,
            height: 2.gh,
          )
        ]
      ],
    );
  }
}
