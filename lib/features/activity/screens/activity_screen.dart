import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/core/theme/custom_text_theme.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/activity/widgets/hot_events.dart';

import '../../home/<USER>/news/news_list.dart';
import '../../market/widgets/market_table_header.dart';
import '../logic/activity/activity_cubit.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';

class ActivityScreen extends StatefulWidget {
  final bool showBackButton;
  final bool showNewsSection;

  const ActivityScreen({
    super.key,
    this.showBackButton = false,
    this.showNewsSection = false,
  });

  @override
  State<ActivityScreen> createState() => _ActivityScreenState();
}

class _ActivityScreenState extends State<ActivityScreen> with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );
    _scaleAnimation = Tween<double>(begin: 0.9, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    // // If news section is hidden, set the initial tab to hot_events
    // if (!widget.showNewsSection) {
    //   WidgetsBinding.instance.addPostFrameCallback((_) {
    //     context.read<ActivityCubit>().updateTab(1);
    //   });
    // }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: widget.showBackButton
          ? AppBar(
              backgroundColor: context.theme.scaffoldBackgroundColor,
              elevation: 0,
              leading: IconButton(
                icon: Icon(
                  Icons.arrow_back_ios,
                  color: context.colorTheme.textPrimary,
                  size: 20.gw,
                ),
                onPressed: () => Navigator.pop(context),
              ),
              title: Text(
                'activity'.tr(),
                style: TextStyle(
                  color: context.colorTheme.textPrimary,
                  fontSize: 18.gsp,
                ),
              ),
            )
          : null,
      body: Padding(
        padding: EdgeInsets.symmetric(horizontal: 16.gw),
        child: SingleChildScrollView(
          child: Column(
            children: [
              BlocBuilder<ActivityCubit, ActivityState>(
                builder: (context, state) {
                  context.locale;
                  return Column(
                    children: [
                      BlocListener<ActivityCubit, ActivityState>(
                        listenWhen: (previous, current) => previous.selectedTab != current.selectedTab,
                        listener: (context, state) {
                          _animationController.forward(from: 0);
                        },
                        child: widget.showNewsSection
                            ? Row(
                                children: [
                                  ScaleTransition(
                                    scale: _scaleAnimation,
                                    child: MarketTableHeader(
                                      title: 'news'.tr(),
                                      isSelected: state.selectedTab == 0,
                                      onTap: () => context.read<ActivityCubit>().updateTab(0),
                                    ),
                                  ),
                                  15.horizontalSpace,
                                  ScaleTransition(
                                    scale: _scaleAnimation,
                                    child: MarketTableHeader(
                                      title: 'hot_events'.tr(),
                                      isSelected: state.selectedTab == 1,
                                      onTap: () => context.read<ActivityCubit>().updateTab(1),
                                    ),
                                  ),
                                ],
                              )
                            : SizedBox.shrink(), // Hide the entire row of tabs when showNewsSection is false
                      ),
                      widget.showNewsSection ? 10.verticalSpace : SizedBox.shrink(),
                      widget.showNewsSection
                          ? AnimatedSwitcher(
                              duration: const Duration(milliseconds: 300),
                              transitionBuilder: (Widget child, Animation<double> animation) {
                                return FadeTransition(
                                  opacity: animation,
                                  child: child,
                                );
                              },
                              child: state.selectedTab == 0
                                  ? NewsList(key: const ValueKey('news'))
                                  : HotEvents(key: const ValueKey('hot_events')),
                            )
                          : HotEvents(key: const ValueKey('hot_events')),
                    ],
                  );
                },
              ),
            ],
          ),
        ),
      ),
    );
  }
}
