import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/invite/logic/invite/invite_cubit.dart';
import 'package:gp_stock_app/features/invite/logic/invite/invite_state.dart';
import 'package:gp_stock_app/shared/app/extension/helper.dart';
import 'package:gp_stock_app/shared/constants/assets.dart';
import 'package:qr_flutter/qr_flutter.dart';

import '../../../core/api/network/endpoint/urls.dart';
import '../../../shared/widgets/error/error_retry_widget.dart';
import '../../../shared/widgets/flip_text.dart';
import '../widgets/invite_shimmer_widget.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/theme/custom_text_theme.dart';

class InviteScreen extends StatefulWidget {
  const InviteScreen({super.key});

  @override
  State<InviteScreen> createState() => _InviteScreenState();
}

class _InviteScreenState extends State<InviteScreen> {
  @override
  void initState() {
    super.initState();
    context.read<InviteCubit>().getInviteDetails();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('inviteAndEarn'.tr()),
        backgroundColor: context.colorTheme.stockRed,
        elevation: 0,
        foregroundColor: Colors.white,
      ),
      body: BlocBuilder<InviteCubit, InviteState>(
        builder: (context, state) {
          if (state.status.isLoading) {
            return const InviteScreenShimmer();
          }

          if (state.status.isFailed) {
            return ErrorRetryWidget(
              errorMessage: state.error,
              onRetry: () => context.read<InviteCubit>().getInviteDetails(),
            );
          }

          return Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  context.colorTheme.stockRed,
                  const Color(0xFFFFC6C6),
                ],
              ),
            ),
            child: Column(
              children: [
                _buildHeader(),
                Expanded(
                  child: Container(
                    padding: EdgeInsets.symmetric(horizontal: 16.gw),
                    child: SingleChildScrollView(
                      child: Column(
                        children: [
                          Container(
                            decoration: BoxDecoration(
                              color: context.theme.dividerColor,
                              borderRadius: BorderRadius.all(
                                Radius.circular(12.gr),
                              ),
                            ),
                            margin: EdgeInsets.only(top: 10.gh),
                            padding: EdgeInsets.all(16.gr),
                            child: Column(
                              children: [
                                _buildReferralOptions(state),
                                SizedBox(height: 20.gh),
                                _buildGenerateButton(),
                              ],
                            ),
                          ),
                          SizedBox(height: 20.gh),
                          _buildCommissionInfo(state),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildHeader() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Text(
          'inviteAndEarnDescription'.tr(),
          style: context.textTheme.primary.fs24.w700.copyWith(
            color: Colors.amber[50]!,
          ),
          textAlign: TextAlign.center,
        ),
        SizedBox(height: 8.gh),
        Text(
          'performCashTradeToEarn'.tr(),
          style: context.textTheme.regular.fs16.w400.copyWith(
            color: Colors.amber[50]!,
          ),
          textAlign: TextAlign.center,
        ),
        16.verticalSpace,
        Stack(
          alignment: Alignment.center,
          children: [
            SizedBox(
              height: 150.gh,
              width: 1.gsw,
              child: Image.asset(
                Assets.grabCoinsIcon,
                fit: BoxFit.cover,
              ),
            ),
            Positioned(
              bottom: 0,
              child: Container(
                width: 330.gw,
                padding: EdgeInsets.symmetric(horizontal: 8.gw, vertical: 6.gh),
                decoration: BoxDecoration(
                  color: context.theme.dividerColor.withValues(alpha: 0.3),
                  borderRadius: BorderRadius.circular(20.gr),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'levelingUpToEarnMore'.tr(),
                      style: context.textTheme.regular.fs12.w400.copyWith(
                        color: context.theme.dividerColor,
                      ),
                    ),
                    Container(
                      padding: EdgeInsets.symmetric(horizontal: 6.gw, vertical: 6.gh),
                      decoration: BoxDecoration(
                        color: const Color(0xFFE06821),
                        borderRadius: BorderRadius.circular(20.gr),
                      ),
                      child: Text(
                        'GO',
                        style: context.textTheme.primary.fs12.w700.copyWith(
                          color: context.theme.dividerColor,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildReferralOptions(InviteState state) {
    final inviteCode = state.inviteDetail?.inviteCode ?? '';
    final inviteLink = '${Urls.inviteLink}$inviteCode';

    return Row(
      children: [
        Expanded(
          child: GestureDetector(
            onTap: () {
              _showQrCodeDialog(context, inviteLink);
            },
            child: Container(
              padding: EdgeInsets.symmetric(vertical: 16.gh, horizontal: 16.gw),
              decoration: BoxDecoration(
                color: Colors.pink[50]!,
                borderRadius: BorderRadius.circular(10.gr),
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.qr_code_2,
                    size: 22.gr,
                    color: context.colorTheme.stockRed,
                  ),
                  SizedBox(height: 10.gh),
                  Text(
                    'qrCode'.tr(),
                    style: context.textTheme.stockRed.fs14.w400,
                  ),
                ],
              ),
            ),
          ),
        ),
        SizedBox(width: 16.gw),
        Expanded(
          child: GestureDetector(
            onTap: () {
              Clipboard.setData(ClipboardData(text: inviteCode));
              Helper.showFlutterToast('inviteCodeCopiedToClipboard'.tr());
            },
            child: Container(
              padding: EdgeInsets.symmetric(vertical: 16.gh, horizontal: 16.gw),
              decoration: BoxDecoration(
                color: Colors.pink[50]!,
                borderRadius: BorderRadius.circular(10.gr),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Flexible(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Text(
                              inviteCode,
                              style: context.textTheme.stockRed.fs14.w400,
                              overflow: TextOverflow.ellipsis,
                            ),
                            8.horizontalSpace,
                            Icon(
                              Icons.copy,
                              size: 20.gr,
                              color: context.colorTheme.stockRed,
                            ),
                          ],
                        ),
                        10.verticalSpace,
                        Text(
                          'inviteCode'.tr(),
                          style: context.textTheme.stockRed.fs14.w400,
                        ),
                      ],
                    ),
                  ),
                  SizedBox(width: 8.gw),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildGenerateButton() {
    return BlocBuilder<InviteCubit, InviteState>(
      builder: (context, state) {
        final inviteCode = state.inviteDetail?.inviteCode ?? '';
        final inviteLink = '${Urls.inviteLink}$inviteCode';

        return ElevatedButton.icon(
          onPressed: () {
            Clipboard.setData(ClipboardData(text: inviteLink));
            Helper.showFlutterToast('inviteLinkCopiedToClipboard'.tr());
          },
          style: ElevatedButton.styleFrom(
            minimumSize: Size(double.infinity, 38.gh),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10.gr),
            ),
          ),
          icon: Icon(Icons.link, color: context.theme.cardColor),
          label: Text(
            'generateInviteLink'.tr(),
            style: context.textTheme.button.fs16.w600.copyWith(
              color: context.theme.cardColor,
            ),
          ),
        );
      },
    );
  }

  Widget _buildCommissionInfo(InviteState state) {
    return Container(
      padding: EdgeInsets.all(16.gr),
      decoration: BoxDecoration(
        color: context.theme.dividerColor,
        borderRadius: BorderRadius.circular(10.gr),
        boxShadow: [
          BoxShadow(
            color: context.theme.shadowColor,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Text(
                      'commissionRate'.tr(),
                      style: context.textTheme.regular.fs14.w400,
                    ),
                    SizedBox(height: 10.gh),
                    Text(
                      '${state.inviteDetail?.commissionRate?.toStringAsFixed(2) ?? 0.00}%',
                      style: context.textTheme.stockRed.fs24.w700,
                    ),
                  ],
                ),
              ),
              Container(
                height: 50.gh,
                width: 1,
                color: context.theme.dividerColor,
              ),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Text(
                      'interestRateRatio'.tr(),
                      style: context.textTheme.regular.fs14.w400,
                    ),
                    SizedBox(height: 10.gh),
                    Text(
                      '${state.inviteDetail?.interestRate?.toStringAsFixed(2) ?? 0.00}%',
                      style: context.textTheme.stockRed.fs24.w700,
                    ),
                  ],
                ),
              ),
            ],
          ),
          SizedBox(height: 20.gh),
          Divider(
            height: 1,
            color: context.theme.dividerColor,
          ),
          SizedBox(height: 20.gh),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'myCommission'.tr(),
                style: context.textTheme.regular.fs14.w400,
              ),
              FlipText(
                state.inviteDetail?.totalCommission ?? 0.00,
                style: context.textTheme.regular.fs14.w700,
                isCurrency: true,
              ),
            ],
          ),
        ],
      ),
    );
  }

  void _showQrCodeDialog(BuildContext context, String inviteLink) {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        backgroundColor: context.theme.cardColor,
        child: Container(
          padding: EdgeInsets.all(24.gr),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              QrImageView(
                data: inviteLink,
                version: QrVersions.auto,
                size: 200.0,
                backgroundColor: context.theme.cardColor,
                padding: EdgeInsets.all(16.gr),
              ),
              SizedBox(height: 16.gh),
              Text(
                'scanQrCodeToAdd'.tr(),
                style: context.textTheme.regular.fs16.w600,
              ),
              SizedBox(height: 8.gh),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  TextButton(
                    onPressed: () {
                      Navigator.of(context).pop();
                    },
                    child: Text(
                      'cancel'.tr(),
                      style: TextStyle(
                        color: context.colorTheme.stockRed,
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
