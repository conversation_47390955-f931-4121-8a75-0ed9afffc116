import 'package:flutter/material.dart';
import 'package:gp_stock_app/core/theme/custom_text_theme.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/shared/theme/font_pallette.dart';
import 'package:gp_stock_app/shared/theme/my_color_scheme.dart';
import 'package:keyboard_actions/keyboard_actions.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';

class FTradeBuySellTextField extends StatelessWidget {
  static const double _buttonSize = 20.0;
  static const double _buttonRadius = 4.0;
  static const double _iconSize = 16.0;
  static const double _horizontalSpacing = 8.0;

  final VoidCallback? onIncrementPressed;
  final VoidCallback? onDecrementPressed;
  final VoidCallback onTap;
  final TextEditingController controller;
  final FocusNode focusNode;
  const FTradeBuySellTextField({
    super.key,
    required this.onIncrementPressed,
    required this.onDecrementPressed,
    required this.controller,
    required this.focusNode,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 35.gh,
      padding: EdgeInsets.symmetric(horizontal: 12.gw),
      decoration: BoxDecoration(
        color: context.theme.scaffoldBackgroundColor,
        borderRadius: BorderRadius.circular(5.gr),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        spacing: _horizontalSpacing,
        children: [
          _buildControlButton(
            context: context,
            icon: Icons.remove,
            onPressed: onDecrementPressed,
          ),
          Expanded(child: _buildTextField(context)),
          _buildControlButton(
            context: context,
            icon: Icons.add,
            onPressed: onIncrementPressed,
          ),
        ],
      ),
    );
  }

  /// Builds the text input field
  Widget _buildTextField(BuildContext context) {
    return KeyboardActions(
      config: _buildKeyboardActionsConfig(context),
      child: TextField(
        focusNode: focusNode,
        controller: controller,
        keyboardType: const TextInputType.numberWithOptions(decimal: true),
        // inputFormatters: [
        //   FilteringTextInputFormatter.allow(RegExp(r'[\d\.,]')),
        //   CommaReplacementFormatter(),
        // ],
        onTap: onTap,
        decoration: InputDecoration(
          border: InputBorder.none,
          focusedBorder: InputBorder.none,
          hintStyle: FontPalette.normal13,
          isDense: true,
          contentPadding: EdgeInsets.symmetric(
            horizontal: _horizontalSpacing.gw,
            vertical: _horizontalSpacing.gh,
          ),
        ),
        textAlign: TextAlign.center,
        style: FontPalette.normal13.copyWith(
          color: context.colorTheme.textRegular,
        ),
      ),
    );
  }

  /// Builds the increment/decrement button
  Widget _buildControlButton({
    required BuildContext context,
    required IconData icon,
    required VoidCallback? onPressed,
  }) {
    return GestureDetector(
      onTap: onPressed,
      child: Container(
        height: _buttonSize.gh,
        width: _buttonSize.gw,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(_buttonRadius.gr),
          color: context.theme.cardColor,
        ),
        child: Center(
          child: Icon(
            icon,
            size: _iconSize.gr,
            color: onPressed == null ? context.theme.dividerColor : context.colorTheme.textRegular,
          ),
        ),
      ),
    );
  }

  KeyboardActionsConfig _buildKeyboardActionsConfig(BuildContext context) {
    return KeyboardActionsConfig(
      keyboardActionsPlatform: KeyboardActionsPlatform.ALL,
      actions: [
        KeyboardActionsItem(focusNode: focusNode),
      ],
    );
  }
}
