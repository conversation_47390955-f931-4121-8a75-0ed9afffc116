import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:gp_stock_app/core/theme/custom_text_theme.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/shared/constants/assets.dart';
import 'package:gp_stock_app/shared/theme/color_pallette.dart';
import 'package:gp_stock_app/shared/theme/font_pallette.dart';
import 'package:gp_stock_app/shared/theme/my_color_scheme.dart';
import 'package:gp_stock_app/shared/widgets/buttons/custom_material_button.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/shared/app/extension/color_extension.dart';

class FTradeKLineToolBar extends StatelessWidget {
  final VoidCallback onWarningBtnClick;
  final VoidCallback onWishBtnClick;
  final VoidCallback onTradeBtnClick;
  final bool? isWish;

  const FTradeKLineToolBar({
    required this.onWarningBtnClick,
    required this.onWishBtnClick,
    required this.onTradeBtnClick,
    this.isWish,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    final double bottomSafeHeight = MediaQuery.of(context).viewPadding.bottom;
    final double screenWidth = MediaQuery.of(context).size.width;

    return Align(
      alignment: Alignment.bottomCenter,
      child: Column(
        children: [
          Container(
            height: 60,
            decoration: BoxDecoration(
              color: context.theme.cardColor,
              boxShadow: [
                BoxShadow(
                  color: Color(0xFF000000),
                  blurRadius: 33,
                  spreadRadius: 5,
                  offset: Offset(10, 20),
                ),
              ],
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(20),
                topRight: Radius.circular(20),
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                _dataCell(
                  context: context,
                  icon: Assets.bellIcon,
                  text: 'alert'.tr(),
                  onTap: onWarningBtnClick,
                  isEnabled: false,
                ),
                Visibility(
                  visible: isWish != null,
                  maintainSize: true,
                  maintainAnimation: true,
                  maintainState: true,
                  child: _WishButton(isWish: isWish ?? false, onWishBtnClick: onWishBtnClick),
                ),
                10.horizontalSpace,
                SizedBox(
                  height: 38.gh,
                  width: 128.gw,
                  child: CustomMaterialButton(
                    buttonText: 'trade2'.tr(),
                    borderColor: ColorPalette.primaryColor,
                    color: ColorPalette.primaryColor,
                    borderRadius: 5,
                    onPressed: onTradeBtnClick,
                  ),
                ),
              ],
            ),
          ),
          Container(
            color: context.theme.cardColor,
            width: screenWidth,
            height: bottomSafeHeight,
          )
        ],
      ),
    );
  }

  Widget _dataCell({
    required String icon,
    String? selectedIcon,
    required String text,
    required VoidCallback onTap,
    required bool isEnabled,
    required BuildContext context,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Container(
            height: 30.gh,
            width: 30.gw,
            padding: EdgeInsets.all(6.gr),
            decoration: BoxDecoration(
              color: context.theme.cardColor,
              borderRadius: BorderRadius.circular(10),
              boxShadow: [
                BoxShadow(
                  color: Theme.of(context).brightness == Brightness.dark
                      ? ColorPalette.shadowColorDark.withNewOpacity(0.3)
                      : Color(0xFFD3D1D8).withNewOpacity(0.3),
                  blurRadius: 16,
                  spreadRadius: 0,
                  offset: Offset(4, 9),
                ),
              ],
            ),
            child: SvgPicture.asset(
              isEnabled ? selectedIcon ?? icon : icon,
            ),
          ),
          Text(text, style: FontPalette.normal12.copyWith(color: ColorPalette.subTitleColor)),
        ],
      ),
    );
  }
}

class _WishButton extends StatelessWidget {
  final bool isWish;
  final VoidCallback onWishBtnClick;
  const _WishButton({required this.isWish, required this.onWishBtnClick});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onWishBtnClick,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Container(
            height: 30.gh,
            width: 30.gw,
            padding: EdgeInsets.all(6.gr),
            decoration: BoxDecoration(
              color: context.theme.cardColor,
              borderRadius: BorderRadius.circular(10),
              boxShadow: [
                BoxShadow(
                  color: Theme.of(context).brightness == Brightness.dark
                      ? Color.fromARGB(255, 64, 54, 90).withNewOpacity(0.3)
                      : Color(0xFFD3D1D8).withNewOpacity(0.3),
                  blurRadius: 16,
                  spreadRadius: 0,
                  offset: Offset(4, 9),
                ),
              ],
            ),
            child: SvgPicture.asset(
              isWish ? Assets.filledHeartIcon : Assets.heartIcon,
            ),
          ),
          Text(
            'watchlistButton'.tr(),
            style: FontPalette.normal12.copyWith(color: context.colorTheme.textRegular),
          ),
        ],
      ),
    );
  }
}
