import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bounceable/flutter_bounceable.dart';
import 'package:gp_stock_app/core/theme/custom_text_theme.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market/f_trade_all_info/f_trade_k_line/domain/f_trade_tick_model.dart';
import 'package:k_chart_plus/chart_style.dart';
import 'package:k_chart_plus/entity/k_line_entity.dart';
import 'package:k_chart_plus/k_chart_widget.dart' as chart_widget;
import 'package:k_chart_plus/renderer/main_renderer.dart';
import 'package:k_chart_plus/utils/data_util.dart';

import 'package:gp_stock_app/features/account/domain/constants/kline_constants.dart';
import 'package:gp_stock_app/features/account/domain/models/kline_option.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market/f_trade_all_info/f_trade_k_line/domain/f_trade_k_line_model.dart';

import 'package:gp_stock_app/shared/app/extension/time_zone.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';
import 'package:gp_stock_app/shared/models/dropdown/dropdown_value.dart';
import 'package:gp_stock_app/shared/theme/font_pallette.dart';
import 'package:gp_stock_app/shared/theme/my_color_scheme.dart';
import 'package:gp_stock_app/shared/widgets/dropdown/common_dropdown.dart';
import 'package:gp_stock_app/shared/widgets/shimmer/shimmer_widget.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';

class FTradeKLineView extends StatefulWidget {
  /// 唯一标识
  final String instrument;
  final bool isTimeOrKline;

  /// 1min/5min/15min/30min/60min/day/week/month/year
  final String period;
  final DataStatus dataStatus;
  final Map<String, FTradeKLineModel> klineMap;
  final FTradeTickModel fTradeTickModel;
  final void Function(bool, String) onPeriodBarClick;
  final VoidCallback needLoadMoreTick;
  const FTradeKLineView({
    super.key,
    required this.instrument,
    required this.klineMap,
    required this.dataStatus,
    required this.period,
    required this.onPeriodBarClick,
    required this.isTimeOrKline,
    required this.needLoadMoreTick,
    required this.fTradeTickModel,
  });

  @override
  State<FTradeKLineView> createState() => _FTradeKLineViewState();
}

class _FTradeKLineViewState extends State<FTradeKLineView> {
  late final String instrument;

  bool isInitial = true;
  KlineOption selectedKlineOption = KlineOption(id: "intraday", label: "realtime_day", period: "day", type: "timeLine");
  final allOption = {
    "t_day": KlineOption(id: "intraday", label: "realtime_day", period: "day", type: "timeLine"),
    "t_5day": KlineOption(id: "5day", label: "realtime_5day", period: "5day", type: "timeLine"),
    "k_day": KlineOption(id: "daily-kline", label: "kline_day", period: "day", type: "kline"),
    "k_week": KlineOption(id: "weekly-kline", label: "kline_week", period: "week", type: "kline"),
    "k_month": KlineOption(id: "monthly-kline", label: "kline_month", period: "month", type: "kline"),
    "k_year": KlineOption(id: "yearly-kline", label: "kline_year", period: "year", type: "kline"),
    "k_1min": KlineOption(id: "1min-kline", label: "kline_1min", period: "1min", type: "kline"),
    "k_5min": KlineOption(id: "5min-kline", label: "kline_5min", period: "5min", type: "kline"),
    "k_15min": KlineOption(id: "15min-kline", label: "kline_15min", period: "15min", type: "kline"),
    "k_30min": KlineOption(id: "30min-kline", label: "kline_30min", period: "30min", type: "kline"),
  };

  @override
  Widget build(BuildContext context) {
    if (widget.isTimeOrKline) {
      selectedKlineOption = allOption["t_${widget.period}"]!;
    } else {
      selectedKlineOption = allOption["k_${widget.period}"]!;
    }

    return Container(
      margin: EdgeInsets.fromLTRB(14, 0, 14, 0),
      decoration: BoxDecoration(
        color: context.theme.cardColor,
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: Color(0x14354677), // #35467714，8%
            offset: Offset(0, 4),
            blurRadius: 8,
            spreadRadius: 0,
          ),
        ],
      ),
      child: Column(
        children: [
          _buildKlineSelector(
            widget.dataStatus,
            selectedKlineOption,
          ),
          Container(
            height: 1,
            color: context.theme.dividerColor,
            width: double.infinity,
          ),
          _buildKLineView(),
        ],
      ),
    );
  }

  Widget _buildKlineSelector(DataStatus dataStatus, KlineOption selectedOption) {
    final isEnglish = context.locale.languageCode == 'en';

    return AbsorbPointer(
      absorbing: dataStatus == DataStatus.loading, // 点击切换数据后载入中 无法再次点击 等待载入完毕
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: Row(
          spacing: 0,
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            SizedBox(width: 10),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: KlineConstants.options.map((option) {
                bool isSelected = selectedOption.id == option.id;
                return SizedBox(
                  width: 40,
                  child: Bounceable(
                    onTap: () {
                      widget.onPeriodBarClick(option.type == "timeLine", option.period);
                    },
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.start,
                      spacing: 4,
                      children: [
                        Text(
                          option.label.tr(),
                          style: FontPalette.bold10.copyWith(
                            color: getKlineSelectorTitleColor(context, isSelected, dataStatus),
                          ),
                        ),
                        Opacity(
                          opacity: isSelected ? 1 : 0,
                          child: Container(
                            width: 20,
                            height: 2,
                            color: dataStatus == DataStatus.loading
                                ? context.colorTheme.textRegular.withValues(alpha: 0.3)
                                : context.theme.primaryColor,
                          ),
                        ),
                      ],
                    ),
                  ),
                );
              }).toList(),
            ),
            Builder(builder: (context) {
              final exists = KlineConstants.subOptions.any((e) => e.id == selectedOption.id);
              final option = exists ? selectedOption : KlineConstants.subOptions[0];
              return Stack(
                children: [
                  SizedBox(
                    width: isEnglish ? 140 : 100,
                    height: 30,
                  ),
                  Positioned(
                    top: -2,
                    child: Container(
                      width: isEnglish ? 140 : 100,
                      height: 28,
                      color: Colors.transparent,
                      child: CommonDropdown(
                        isSuperDense: true,
                        showSearchBox: false,
                        isEnabled: true,
                        selectedItem: DropDownValue(
                          value: option.label.tr(),
                          id: option.id,
                        ),
                        dropDownValue: KlineConstants.subOptions
                            .map((e) => DropDownValue(
                                  value: e.label.tr(),
                                  id: e.id,
                                ))
                            .toList(),
                        onChanged: (value) {
                          final selectedOption = KlineConstants.subOptions.firstWhere(
                            (e) => e.id == value.id,
                            orElse: () => KlineConstants.subOptions[0],
                          );
                          widget.onPeriodBarClick(selectedOption.type == "timeLine", selectedOption.period);
                        },
                        hintText: '',
                        fillColor: Colors.transparent,
                        borderRadius: 5.gr,
                        textStyle: FontPalette.bold10.copyWith(
                          color: getKlineSelectorTitleColor(context, exists, dataStatus),
                        ),
                      ),
                    ),
                  ),
                  Positioned(
                    bottom: 4,
                    left: 0,
                    right: 0,
                    child: Center(
                      child: Opacity(
                        opacity: exists ? 1 : 0,
                        child: Container(
                          width: 30,
                          height: 2,
                          color: dataStatus == DataStatus.loading
                              ? context.colorTheme.textRegular.withValues(alpha: 0.3)
                              : context.theme.primaryColor,
                        ),
                      ),
                    ),
                  ),
                ],
              );
            }),
            SizedBox(width: 30),
          ],
        ),
      ),
    );
  }

  Widget _buildKLineView() {
    String periodKey;
    if (widget.isTimeOrKline) {
      periodKey = "t_${widget.period}";
    } else {
      periodKey = "k_${widget.period}";
    }

    FTradeInfoKLineModel? klineModelDetail = widget.klineMap[periodKey]?.detail;
    List<FTradeKLineItem> klineModelList = widget.klineMap[periodKey]?.list ?? [];

    final showTicks = periodKey == "t_day";
    final scaleX = switch (periodKey) {
      "k_week" => 0.5,
      "k_month" => 0.5,
      "k_year" => 1.0,
      "t_day" => 0.15,
      "t_5day" => 0.03,
      _ => 0.8,
    };

    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        Expanded(
          child: Padding(
            padding: const EdgeInsets.fromLTRB(0, 0, 0, 4),
            child: FutureBuilder<List<KLineEntity>>(
              future: _processData(klineModelList),
              builder: (context, snapshot) {
                return Stack(
                  children: [
                    Padding(
                      padding: const EdgeInsets.fromLTRB(0, 10, 0, 0),
                      child: chart_widget.KChartWidget(
                        snapshot.data ?? [],
                        ChartStyle(),
                        ChartColors(
                          upColor: const Color(0xFFD2544F),
                          dnColor: const Color(0xFF5DAF78),
                          gridColor: Colors.transparent,
                          bgColor: context.theme.cardColor,
                        ),
                        getColorCallback: (value) => value.getValueColor(context),
                        mBaseHeight: 0.27.gsh,
                        isTrendLine: false,
                        scaleX: scaleX,
                        mainState: chart_widget.MainState.MA,
                        volHidden: false,
                        isTapShowInfoDialog: true,
                        secondaryStateLi: {},
                        timeFormat: chart_widget.TimeFormat.YEAR_MONTH_DAY_WITH_HOUR,
                        verticalTextAlignment: VerticalTextAlignment.right,
                        isLine: widget.isTimeOrKline,
                        xFrontPadding: 0,
                        closePrice: klineModelDetail?.close,
                        showDate: !showTicks,
                        locale: context.locale.languageCode,
                        isMarketOpen:
                            true, // TODO: getIt<MarketStatusCubit>().isMarketOpen(state.$2?.data?.detail?.market),
                      ),
                    ),
                    if (widget.dataStatus == DataStatus.loading)
                      Positioned.fill(
                        child: Center(
                          child: CircularProgressIndicator(color: context.theme.primaryColor),
                        ),
                      )
                  ],
                );
              },
            ),
          ),
        ),
        if (showTicks)
          FtradeTickList(
            getColorCallback: (value) => value.getValueColor(context),
            tickModel: widget.fTradeTickModel,
            dataStatus: DataStatus.idle,
            needLoadMoreTick: widget.needLoadMoreTick,
          ),
      ],
    );
  }

  Color? getKlineSelectorTitleColor(BuildContext context, bool isSelected, DataStatus status) {
    if (status == DataStatus.loading) {
      return context.colorTheme.textPrimary.withValues(alpha: 0.3);
    }
    if (isSelected) {
      return context.theme.primaryColor;
    }
    return null;
  }

  Future<List<KLineEntity>> _processData(List<FTradeKLineItem>? list) async {
    if (list == null) return [];

    final List<KLineEntity> klineEntities = [];
    double? previousPrice;
    final int offset = countryTimeOffsets['CN'] ?? 0;

    for (final item in list) {
      final open = previousPrice ?? (widget.isTimeOrKline ? item.price : item.open);

      final klineEntity = item.toKLineEntity(
        isTimeOrKline: widget.isTimeOrKline,
        openPrice: open,
        timeOffsetHours: offset,
      );

      klineEntities.add(klineEntity);
      previousPrice = item.price;
    }

    DataUtil.calculate(klineEntities);
    return klineEntities;
  }
}

class FtradeTickList extends StatefulWidget {
  final FTradeTickModel tickModel;
  final DataStatus dataStatus;
  final VoidCallback needLoadMoreTick;
  final Color Function(double value)? getColorCallback;
  const FtradeTickList({
    super.key,
    this.getColorCallback,
    required this.tickModel,
    required this.dataStatus,
    required this.needLoadMoreTick,
  });

  @override
  State<FtradeTickList> createState() => _FtradeTickListState();
}

class _FtradeTickListState extends State<FtradeTickList> {
  // final controller = ScrollController();

  @override
  void initState() {
    super.initState();
    // controller.addListener(_onScroll);
  }

  // void _onScroll() {
  //   if (controller.position.pixels == controller.position.maxScrollExtent) {
  //     widget.needLoadMoreTick();
  //   }
  // }

  @override
  void dispose() {
    // controller.removeListener(_onScroll);
    // controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final items = widget.tickModel.records;
    if (items.isEmpty) {
      return SizedBox(
        width: 88,
        height: 0.32.gsh,
      );
    }

    return Container(
      width: 88,
      height: 0.32.gsh,
      color: context.theme.cardColor,
      padding: EdgeInsets.fromLTRB(4, 0, 8, 0),
      child: ListView.builder(
        itemCount: items.length + 1,
        // controller: controller,
        shrinkWrap: true,
        itemBuilder: (context, index) {
          if (index >= items.length) {
            if (widget.tickModel.hasNext == true) {
              return ShimmerWidget(height: 10.gh);
            }
            return const SizedBox.shrink();
          }
          final item = items[index];
          // final time = (item.time.isNotEmpty)
          //     ? TimeZoneHelper.formatTimeInZone(
          //         DateTime.parse(item.time).millisecondsSinceEpoch ~/ 1000,
          //         'Asia/Shanghai', //!check here, should be dynamic
          //         format: TimeFormat.hm,
          //       )
          //     : '--:--';
          final tradePrice = '${item.tradePrice}';

          final tradeVolume =
              item.tradeVolume.toInt(); //formatLargeNumber(item.tradeVolume, context.locale.languageCode);

          final configMap = {
            'S': Colors.greenAccent.shade700,
            'B': Colors.red,
            'N': Colors.grey,
          };

          final directionColor = configMap[item.direction] ?? Colors.grey;

          return Row(
            spacing: 8,
            children: [
              Text(
                tradePrice,
                style: FontPalette.normal8.copyWith(color: directionColor),
              ),
              Spacer(),
              Row(
                spacing: 4,
                children: [
                  Text(
                    tradeVolume.toString(),
                    style: FontPalette.normal8,
                  ),
                  Text(
                    item.direction,
                    style: SecFontPalette.normal8.copyWith(color: directionColor),
                  ),
                ],
              )
            ],
          );
        },
      ),
    );
  }
}
