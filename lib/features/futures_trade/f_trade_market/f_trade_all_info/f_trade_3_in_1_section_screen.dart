import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/core/theme/custom_text_theme.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market/f_trade_all_info/f_trade_k_line/domain/f_trade_k_line_scroll_repository.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market/f_trade_all_info/f_trade_k_line/f_trade_k_line_scroll_view.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market/f_trade_all_info/f_trade_k_line/logic/f_trade_k_line_cubit.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market/f_trade_all_info/f_trade_profile/logic/t_trade_profile_scroll_cubit.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market/f_trade_all_info/f_trade_profile/t_trade_profile_scroll_view.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market/f_trade_list/domain/f_trade_list_model.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market_enum.dart';
import 'package:gp_stock_app/features/market/watch_list/logic/watch_list_cubit.dart';
import 'package:gp_stock_app/shared/theme/color_pallette.dart';
import 'package:gp_stock_app/shared/theme/my_color_scheme.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';

/// 行情 简况 咨询 3和1 页面
class FTrade3In1SectionScreen extends StatefulWidget {
  final FTradeMarketType type;
  final FTradeListItemModel data;
  final void Function(int) onChangeAllInfoScreenTitlesAction;
  const FTrade3In1SectionScreen({
    super.key,
    required this.type,
    required this.data,
    required this.onChangeAllInfoScreenTitlesAction,
  });

  @override
  State<FTrade3In1SectionScreen> createState() => _FTrade3In1SectionScreenState();
}

class _FTrade3In1SectionScreenState extends State<FTrade3In1SectionScreen> with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final titles = ['tabMarket', 'tabProfile'];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: titles.length, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return DefaultTabController(
      length: titles.length,
      child: Column(
        children: [
          Container(
            height: 44,
            decoration: BoxDecoration(color: context.theme.scaffoldBackgroundColor),
            child: TabBar(
              controller: _tabController,
              physics: const NeverScrollableScrollPhysics(),
              padding: EdgeInsets.zero,
              indicatorPadding: EdgeInsets.zero,
              labelPadding: EdgeInsets.zero,
              indicatorColor: ColorPalette.primaryColor,
              labelColor: ColorPalette.primaryColor,
              unselectedLabelColor: context.colorTheme.textRegular,
              labelStyle: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
              unselectedLabelStyle: TextStyle(fontWeight: FontWeight.normal),
              dividerColor: Colors.transparent,
              tabs: List.generate(
                titles.length,
                (index) => Tab(
                  text: titles[index].tr(),
                ),
              ),
            ),
          ),
          Expanded(
            child: TabBarView(
              controller: _tabController,
              physics: const NeverScrollableScrollPhysics(),
              children: [
                MultiBlocProvider(
                  providers: [
                    BlocProvider(create: (_) => FTradeKLineCubit(FTradeKLineScrollRepository())),
                    BlocProvider<WatchListCubit>.value(value: context.read<WatchListCubit>()),
                  ],
                  child: FTradeKLineScrollView(
                    type: widget.type,
                    data: widget.data,
                    onChangeAllInfoScreenTitlesAction: widget.onChangeAllInfoScreenTitlesAction,
                  ),
                ),
                BlocProvider(
                  create: (_) => FTradeProfileScrollCubit()..fetchData(instrument: widget.data.makeInstrument()),
                  child: BlocBuilder<FTradeProfileScrollCubit, FTradeProfileScrollState>(
                    builder: (context, state) {
                      return FTradeProfileScrollView(
                        type: widget.type,
                        data: widget.data,
                        displayList: state.makeDisplayList(),
                      );
                    },
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
