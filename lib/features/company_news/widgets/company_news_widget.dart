import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:gp_stock_app/core/theme/custom_text_theme.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';

import '../domain/models/company_news_details.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';

class CompanyNewsHeader extends StatelessWidget {
  final CompanyNewsDetailsData newsDetails;

  const CompanyNewsHeader({super.key, required this.newsDetails});

  @override
  Widget build(BuildContext context) {
    final dateFormat = DateFormat('yyyy-MM-dd HH:mm:ss');
    final displayFormat = DateFormat('yyyy/MM/dd HH:mm');

    DateTime? parsedDate;
    try {
      parsedDate = dateFormat.parse(newsDetails.articleDate ?? '');
    } catch (e) {
      // Handle parsing error
    }

    final formattedDate = parsedDate != null ? displayFormat.format(parsedDate) : '';

    return Padding(
      padding: EdgeInsets.all(8.gr),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            newsDetails.articleTitle ?? '',
            style: context.textTheme.primary.fs16.w500.copyWith(
              height: 1.4,
            ),
          ),
          10.verticalSpace,
          Row(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              Text(
                formattedDate,
                style: context.textTheme.regular.fs12.w400,
              ),
              16.horizontalSpace,
              Text(
                newsDetails.articleAuth ?? '',
                style: context.textTheme.regular.fs12.w400,
              ),
            ],
          ),
        ],
      ),
    );
  }
}

class CompanyNewsContent extends StatelessWidget {
  final CompanyNewsDetailsData newsDetails;

  const CompanyNewsContent({super.key, required this.newsDetails});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 8.gw),
      child: Column(
        children: [
          if (newsDetails.articleContent != null)
            Html(
              data: newsDetails.articleContent!,
              style: {
                "p": Style(
                  color: context.colorTheme.textPrimary,
                  fontSize: FontSize(14.gsp),
                  fontFamily: 'PingFang SC',
                ),
                "strong": Style(
                  fontWeight: FontWeight.bold,
                ),
              },
            ),
        ],
      ),
    );
  }
}
