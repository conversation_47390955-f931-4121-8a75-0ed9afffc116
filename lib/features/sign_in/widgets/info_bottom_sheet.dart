// info_bottom_sheet.dart
import 'package:flutter/material.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';

import '../../../shared/theme/font_pallette.dart';

class InfoBottomSheet extends StatelessWidget {
  final String title;
  final String content;

  const InfoBottomSheet({
    super.key,
    required this.title,
    required this.content,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.8,
      decoration: BoxDecoration(
        color: context.theme.cardColor,
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(20.gr),
        ),
      ),
      child: Column(
        children: [
          // Handle bar
          Center(
            child: Container(
              margin: EdgeInsets.symmetric(vertical: 12.gh),
              width: 40.gw,
              height: 4.gh,
              decoration: BoxDecoration(
                color: context.theme.dividerColor,
                borderRadius: BorderRadius.circular(2.gr),
              ),
            ),
          ),
          // Title
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 16.gw),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  title,
                  style: FontPalette.semiBold16.copyWith(
                    color: context.colorTheme.textPrimary,
                  ),
                ),
                IconButton(
                  onPressed: () => Navigator.pop(context),
                  icon: Icon(
                    Icons.close,
                    color: context.colorTheme.textPrimary,
                  ),
                ),
              ],
            ),
          ),
          Divider(color: context.theme.dividerColor),
          // Content
          Expanded(
            child: SingleChildScrollView(
              padding: EdgeInsets.all(16.gw),
              physics: const BouncingScrollPhysics(),
              child: Html(
                data: content,
                style: {
                  "p": Style(
                    color: context.colorTheme.textPrimary,
                    fontSize: FontSize(14.gsp),
                    fontFamily: 'PingFang SC',
                  ),
                  "strong": Style(
                    fontWeight: FontWeight.bold,
                  ),
                },
              ),
            ),
          ),
        ],
      ),
    );
  }
}
