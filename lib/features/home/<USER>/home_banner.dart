import 'package:cached_network_image/cached_network_image.dart';
import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/shared/theme/my_color_scheme.dart';

import '../../../core/utils/app_navigation_handler.dart';
import '../../../shared/constants/enums.dart';
import '../../../shared/widgets/shimmer/shimmer_widget.dart';
import '../logic/home/<USER>';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/shared/app/extension/color_extension.dart';

class HomeBanner extends StatefulWidget {
  const HomeBanner({super.key});

  @override
  State<HomeBanner> createState() => _HomeBannerState();
}

class _HomeBannerState extends State<HomeBanner> {
  final CarouselSliderController carouselController = CarouselSliderController();

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<HomeCubit, HomeState>(builder: (context, state) {
      if (state.bannerFetchStatus == DataStatus.loading) {
        return Padding(
          padding: EdgeInsets.symmetric(
            horizontal: 16.gw,
          ),
          child: ShimmerWidget(
            width: double.infinity,
            height: 120.gh,
            radius: 12.gr,
          ),
        );
      }

      return Stack(
        children: [
          CarouselSlider.builder(
            options: CarouselOptions(
              aspectRatio: 373 / 120,
              viewportFraction: 1,
              autoPlay: true,
              autoPlayInterval: const Duration(seconds: 5),
              enableInfiniteScroll: true,
              onPageChanged: (index, reason) => context.read<HomeCubit>().updateBannerIndex(index),
            ),
            itemCount: state.bannerData?.length ?? 0,
            itemBuilder: (BuildContext context, int itemIndex, int pageViewIndex) => InkWell(
              onTap: () {
                if (state.bannerData != null && itemIndex < state.bannerData!.length) {
                  final banner = state.bannerData![itemIndex];
                  AppNavigationHandler.handleNavigation(context, jumpType: banner.jumpType, jumpUrl: banner.jumpUrl);
                }
              },
              child: Container(
                width: double.infinity,
                margin: EdgeInsets.symmetric(
                  horizontal: 16.gw,
                ),
                height: 188.gh,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(8.gr),
                  image: DecorationImage(
                    image: CachedNetworkImageProvider(state.bannerData?[itemIndex].imageUrl ?? ''),
                    fit: BoxFit.cover,
                  ),
                ),
              ),
            ),
            carouselController: carouselController,
          ),
          Positioned(
            bottom: 0,
            left: 0,
            right: 0,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: state.bannerData?.asMap().entries.map((entry) {
                    return GestureDetector(
                      onTap: () => carouselController.animateToPage(entry.key),
                      child: Container(
                        width: 6.0.gw,
                        height: 6.0.gh,
                        margin: EdgeInsets.symmetric(vertical: 8.0, horizontal: 4.0),
                        decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            color: state.bannerIndex == entry.key
                                ? context.theme.primaryColor.withNewOpacity(0.9)
                                : Colors.white.withNewOpacity(0.5)),
                      ),
                    );
                  }).toList() ??
                  [],
            ),
          ),
        ],
      );
    });
  }
}
