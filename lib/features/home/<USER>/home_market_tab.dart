import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/core/dependency_injection/injectable.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market/f_trade_list/domain/f_trade_list_repository.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market/f_trade_list/f_trade_list_screen.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market/f_trade_list/logic/f_trade_list_cubit.dart';
import 'package:gp_stock_app/features/main/logic/main/main_cubit.dart';
import 'package:gp_stock_app/features/market/widgets/visual_graph_section.dart';
import 'package:gp_stock_app/shared/theme/my_color_scheme.dart';

import '../../../shared/theme/font_pallette.dart';
import '../../market/watch_list/widgets/wishlist_data_table.dart';
import '../widgets/market_data_table.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';

class HomeMarketTabsSection extends StatefulWidget {
  const HomeMarketTabsSection({super.key});

  @override
  State<HomeMarketTabsSection> createState() => _HomeMarketTabsSectionState();
}

class _HomeMarketTabsSectionState extends State<HomeMarketTabsSection> {
  int _selectedTabIndex = 0;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 16.gw),
          child: _buildPillTabs(),
        ),
        _selectedTabIndex == 2 ? 0.verticalSpace : 16.verticalSpace,
        _buildTabContent(),
      ],
    );
  }

  Widget _buildPillTabs() {
    return Container(
      height: 35.gh,
      decoration: BoxDecoration(
        color: context.theme.cardColor,
        borderRadius: BorderRadius.circular(12.gr),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          _buildPillTab(0, 'stockIndex'.tr()), // Stock Index
          _buildPillTab(1, 'stocks'.tr()), // Stocks
          // _buildPillTab(2, '合约'), // Contracts
          _buildPillTab(2, 'marketTitle7'.tr()), // Contracts
          _buildPillTab(3, 'watchList'.tr()), // Watchlist
        ],
      ),
    );
  }

  Widget _buildPillTab(int index, String title) {
    final isSelected = _selectedTabIndex == index;

    return Expanded(
      child: GestureDetector(
        onTap: () => setState(() => _selectedTabIndex = index),
        child: Container(
          alignment: Alignment.center,
          decoration: BoxDecoration(
            color: isSelected ? context.theme.primaryColor : Colors.transparent,
            borderRadius: BorderRadius.circular(12.gr),
          ),
          child: Text(
            title,
            style: FontPalette.medium14.copyWith(
              color: isSelected ? Colors.white : context.colorTheme.textRegular,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildTabContent() {
    switch (_selectedTabIndex) {
      case 0:
        return VisualGraphSection(isFromHome: true);
      case 1:
        return Padding(
          padding: EdgeInsets.symmetric(horizontal: 16.gw),
          child: const MarketDataTable(limit: 5, isHome: true),
        );
      case 2:
        return MultiBlocProvider(
          providers: [
            BlocProvider<FTradeListCubit>(
              create: (_) => FTradeListCubit(FTradeListRepository(), showInHomePage: true),
            ),
            // BlocProvider<MainCubit>(create: (_) => getIt<MainCubit>()),
          ],
          child: FTradeListScreen(showInHomePage: true),
        );
      case 3:
        return Padding(
          padding: EdgeInsets.symmetric(horizontal: 16.gw),
          child: const WishListDataTable(limit: 5),
        );

      default:
        return const SizedBox.shrink();
    }
  }
}
