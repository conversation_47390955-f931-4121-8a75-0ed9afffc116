import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bounceable/flutter_bounceable.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';

import '../../../shared/theme/font_pallette.dart';
import '../../../shared/theme/my_color_scheme.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';

class HomeMenuButton extends StatelessWidget {
  final String icon;
  final String title;
  final VoidCallback onTap;
  const HomeMenuButton({super.key, required this.icon, required this.title, required this.onTap});

  @override
  Widget build(BuildContext context) {
    return Bounceable(
      onTap: onTap,
      child: Stack(
        children: [
          Image.asset(icon, width: 110.gw, height: 32.gh),
          Positioned(
            left: 50.gw,
            bottom: 10.gh,
            top: 10.gh,
            child: FittedBox(
              child: Text(
                title.tr(),
                style: FontPalette.bold18.copyWith(color: context.theme.primaryColor),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
