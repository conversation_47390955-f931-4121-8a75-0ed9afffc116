import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_lucide/flutter_lucide.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/home/<USER>/ai_chat/ai_chat_cubit.dart';
import 'package:gp_stock_app/features/home/<USER>/ai_chat/ai_chat_state.dart';
import 'package:gp_stock_app/shared/constants/assets.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';
import 'package:gp_stock_app/shared/theme/my_color_scheme.dart';
import 'package:gp_stock_app/shared/widgets/animated_text.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/shared/app/extension/color_extension.dart';

class AIChatScreen extends StatefulWidget {
  const AIChatScreen({super.key});

  @override
  State<AIChatScreen> createState() => _AIChatScreenState();
}

class _AIChatScreenState extends State<AIChatScreen> {
  final TextEditingController _questionController = TextEditingController();

  @override
  void dispose() {
    _questionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('aiAnalysis'.tr()),
      ),
      body: BlocBuilder<AIChatCubit, AIChatState>(
        builder: (context, state) {
          return Column(
            children: [
              Container(
                width: 0.9.gsw,
                padding: const EdgeInsets.all(16),
                margin: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Theme.of(context).primaryColor,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  'aiSlogan'.tr(),
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 16.gsp,
                    fontWeight: FontWeight.bold,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
              Expanded(
                child: ListView.builder(
                  padding: const EdgeInsets.all(16),
                  itemCount: state.aiChatData.length + 1,
                  itemBuilder: (context, index) {
                    if (state.aiChatData.isEmpty && state.aiChatStatus != DataStatus.loading) {
                      return Stack(
                        alignment: Alignment.center,
                        children: [
                          Image.asset(
                            Assets.robot,
                            width: 0.58.gsw,
                            height: 0.58.gsw,
                          ),
                          Positioned(
                            top: 26.gh,
                            left: 0.17.gsw,
                            child: SizedBox(
                              width: 0.29.gsw,
                              child: Text(
                                'aiIntelligentAnalysis'.tr(),
                                style: TextStyle(
                                  color: context.colorTheme.textRegular,
                                  fontSize: 15.gsp,
                                ),
                                textAlign: TextAlign.center,
                              ),
                            ),
                          ),
                        ],
                      );
                    }
                    if (index == state.aiChatData.length) {
                      if (state.aiChatStatus == DataStatus.loading) {
                        return AnimatedText(
                          text: 'aiAnswerLabel'.tr(),
                          isLoading: true,
                          style: TextStyle(fontSize: 14, color: context.colorTheme.textRegular),
                        );
                      }
                      return const SizedBox(height: 16);
                    }

                    return Container(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            '${'aiQuestionLabel'.tr()}${state.aiChatData[index].question}',
                            style: const TextStyle(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 8),
                          AnimatedText(
                            text: '${'aiAnswerLabel'.tr()}${state.aiChatData[index].reply}',
                            enableAnimation: index == state.aiChatData.length - 1,
                            style: TextStyle(fontSize: 14, color: context.colorTheme.textRegular),
                          ),
                        ],
                      ),
                    );
                  },
                ),
              ),
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: context.theme.cardColor,
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withNewOpacity(0.05),
                      spreadRadius: 1,
                      blurRadius: 5,
                      offset: const Offset(0, -3),
                    ),
                  ],
                ),
                child: Row(
                  children: [
                    Expanded(
                      child: TextField(
                        controller: _questionController,
                        decoration: InputDecoration(
                          hintText: 'aiInputPlaceholder'.tr(),
                          hintStyle: const TextStyle(
                            color: Colors.grey,
                          ),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(25),
                            borderSide: BorderSide(
                              color: Colors.grey.withNewOpacity(0.2),
                            ),
                          ),
                          enabledBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(25),
                            borderSide: BorderSide(
                              color: Colors.grey.withNewOpacity(0.2),
                            ),
                          ),
                          focusedBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(25),
                            borderSide: BorderSide(
                              color: context.theme.primaryColor,
                            ),
                          ),
                          contentPadding: const EdgeInsets.symmetric(
                            horizontal: 20,
                            vertical: 10,
                          ),
                        ),
                        onSubmitted: (value) {
                          if (value.isNotEmpty) {
                            context.read<AIChatCubit>().sendMessage(value);
                            _questionController.clear();
                          }
                        },
                        textInputAction: TextInputAction.send,
                      ),
                    ),
                    const SizedBox(width: 8),
                    ElevatedButton(
                      onPressed: () {
                        if (_questionController.text.isNotEmpty) {
                          context.read<AIChatCubit>().sendMessage(_questionController.text);
                          _questionController.clear();
                        }
                      },
                      style: ElevatedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 24,
                          vertical: 12,
                        ),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(25),
                        ),
                      ),
                      child: Icon(
                        LucideIcons.send,
                        size: 24,
                        color: context.theme.scaffoldBackgroundColor,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          );
        },
      ),
    );
  }
}
