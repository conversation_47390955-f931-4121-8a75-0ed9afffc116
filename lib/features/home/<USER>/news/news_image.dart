import 'package:flutter/material.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/shared/widgets/shimmer/shimmer_widget.dart';

import '../../../../shared/theme/color_pallette.dart';
import '../../../../shared/theme/font_pallette.dart';
import 'package:gp_stock_app/shared/app/extension/color_extension.dart';

class NewsImage extends StatelessWidget {
  final String imageUrl;

  const NewsImage({super.key, required this.imageUrl});

  @override
  Widget build(BuildContext context) {
    return ClipRRect(
      borderRadius: BorderRadius.circular(8.gr),
      child: Container(
        color: Colors.grey[200],
        width: 120.gw,
        height: 80.gh,
        child: Image.network(
          imageUrl,
          fit: BoxFit.cover,
          loadingBuilder: (_, child, loadingProgress) {
            if (loadingProgress == null) return child;
            return const _NewsImageShimmer();
          },
          errorBuilder: (_, __, ___) => const _ErrorImage(),
        ),
      ),
    );
  }
}

class _NewsImageShimmer extends StatelessWidget {
  const _NewsImageShimmer();

  @override
  Widget build(BuildContext context) {
    return ShimmerWidget(
      width: 120.gw,
      height: 80.gh,
    );
  }
}

class _ErrorImage extends StatelessWidget {
  const _ErrorImage();

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 120.gw,
      height: 80.gh,
      decoration: BoxDecoration(
        color: ColorPalette.primaryColor.withNewOpacity(0.05),
        borderRadius: BorderRadius.circular(8.gr),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.image_not_supported_rounded,
            color: ColorPalette.primaryColor.withNewOpacity(0.3),
            size: 24.gw,
          ),
          4.verticalSpace,
          Text(
            'Image not available',
            style: FontPalette.semiBold9.copyWith(
              color: ColorPalette.primaryColor.withNewOpacity(0.3),
            ),
          ),
        ],
      ),
    );
  }
}
