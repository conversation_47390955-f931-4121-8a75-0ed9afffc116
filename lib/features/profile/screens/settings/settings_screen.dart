import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/core/theme/custom_text_theme.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';
import 'package:gp_stock_app/shared/logic/sort_color/sort_color_cubit.dart';
import 'package:gp_stock_app/shared/theme/my_color_scheme.dart';

import '../../../../core/utils/cache_utils.dart';
import '../../../../shared/app/extension/helper.dart';
import '../../../../shared/app/utilities/easy_loading.dart';
import '../../../../shared/logic/theme/theme_cubit.dart';
import '../../../../shared/routes/routes.dart';
import '../../../../shared/theme/color_pallette.dart';
import '../../../../shared/widgets/list_tile/list_tile.dart';
import '../../widgets/settings/lanaguage_dialog.dart';
import '../../widgets/settings/settings_appbar.dart';
import '../../widgets/settings/stock_order_dialog.dart';
import '../../widgets/settings/theme_dialog.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';

class SettingsScreen extends StatelessWidget {
  const SettingsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    Future<void> showClearCacheDialog(BuildContext context, Function refreshCache) async {
      return showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: Text('confirmClearCache'.tr()),
          content: Text('confirmClearCacheMsg'.tr()),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12.gr),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: Text(
                'cancel'.tr(),
                style: TextStyle(color: Colors.grey),
              ),
            ),
            TextButton(
              onPressed: () async {
                Navigator.pop(context);

                // Show loading
                GPEasyLoading.showLoading(message: 'clearing'.tr());

                // Clear cache
                await clearAppCache();

                // Show success and refresh size
                GPEasyLoading.showSuccess(message: 'clearSuccess'.tr());
                refreshCache();
              },
              child: Text(
                'confirm'.tr(),
                style: TextStyle(color: context.theme.primaryColor),
              ),
            ),
          ],
        ),
      );
    }

    return Scaffold(
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      appBar: SettingsAppBar(title: 'systemSettings'.tr()),
      body: Padding(
        padding: EdgeInsets.symmetric(horizontal: 14.gw),
        child: Column(
          spacing: 10.gh,
          children: [
            4.verticalSpace,
            CommonListTile(
              title: 'personalInfo'.tr(),
              onTap: () => Navigator.pushNamed(context, routePersonalInfoEdit),
            ),
            Container(
              decoration: BoxDecoration(
                color: context.theme.cardColor,
                borderRadius: BorderRadius.circular(8.gr),
              ),
              child: Column(
                children: [
                  CommonListTile(
                    title: 'password_account'.tr(),
                    onTap: () =>
                        Navigator.pushNamed(context, routePassword, arguments: {'type': PasswordModifyType.account}),
                  ),
                  CommonListTile(
                    title: 'password_financial'.tr(),
                    onTap: () =>
                        Navigator.pushNamed(context, routePassword, arguments: {'type': PasswordModifyType.financial}),
                    showBorder: false,
                  ),
                ],
              ),
            ),
            _PreferencesSection(),
            _CacheListTile(
              borderRadius: 8.gr,
              onTap: (refreshCache) => showClearCacheDialog(context, refreshCache),
              showBorder: false,
            ),
          ],
        ),
      ),
    );
  }
}

class _PreferencesSection extends StatelessWidget {
  const _PreferencesSection();

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: context.theme.cardColor,
        borderRadius: BorderRadius.circular(8.gr),
      ),
      child: Column(
        children: [
          CommonListTile(
            borderRadius: 8.gr,
            title: 'language'.tr(),
            value: Helper().getLanguageName(context.locale),
            onTap: () => showModalBottomSheet(
              backgroundColor: ColorPalette.backgroundColor,
              context: context,
              builder: (context) => LanguageDialog(),
            ),
          ),
          BlocSelector<ThemeCubit, ThemeState, ThemeMode>(
            selector: (state) => state.themeMode,
            builder: (context, themeMode) {
              return CommonListTile(
                borderRadius: 0.gr,
                title: 'theme'.tr(),
                value: themeMode == ThemeMode.dark ? 'darkTheme'.tr() : 'lightTheme'.tr(),
                onTap: () => showModalBottomSheet(
                  context: context,
                  builder: (context) => ThemeDialog(),
                ),
              );
            },
          ),
          BlocBuilder<SortColorCubit, SortColorState>(
            builder: (context, state) {
              return CommonListTile(
                borderRadius: 8.gr,
                title: 'priceColor'.tr(),
                showBorder: false,
                value: state.marketColor.tr.tr(),
                onTap: () => showModalBottomSheet(
                  context: context,
                  builder: (context) => StockOrderDialog(),
                ),
              );
            },
          ),
        ],
      ),
    );
  }
}

class _CacheListTile extends StatefulWidget {
  final double borderRadius;
  final Function(Function refreshCache) onTap;
  final bool showBorder;

  const _CacheListTile({
    required this.borderRadius,
    required this.onTap,
    this.showBorder = true,
  });

  @override
  State<_CacheListTile> createState() => _CacheListTileState();
}

class _CacheListTileState extends State<_CacheListTile> {
  String _cacheSize = '0.00';

  @override
  void initState() {
    super.initState();
    _loadCacheSize();
  }

  Future<void> _loadCacheSize() async {
    final size = await getCacheSize();
    if (mounted) {
      setState(() {
        _cacheSize = size.toStringAsFixed(2);
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return CommonListTile(
      borderRadius: widget.borderRadius,
      title: 'clearCache'.tr(),
      value: '$_cacheSize KB',
      onTap: () => widget.onTap(_loadCacheSize),
      showBorder: widget.showBorder,
    );
  }
}
