import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_bounceable/flutter_bounceable.dart';
import 'package:gp_stock_app/core/services/app_info_service.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/profile/widgets/about_us/about_us_shimmer.dart';
import 'package:gp_stock_app/shared/routes/routes.dart';
import 'package:gp_stock_app/shared/theme/font_pallette.dart';
import 'package:gp_stock_app/shared/theme/my_color_scheme.dart';

import '../../../../core/dependency_injection/injectable.dart';
import '../../../../shared/constants/constants.dart';
import '../../../../shared/constants/enums.dart';
import '../../logic/app_info/app_info_cubit.dart';
import '../../logic/app_info/app_info_state.dart';
import '../../widgets/settings/settings_appbar.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/shared/app/extension/color_extension.dart';

class AboutUsScreen extends StatelessWidget {
  const AboutUsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: SettingsAppBar(title: 'aboutUs'.tr()),
      body: Padding(
        padding: EdgeInsets.symmetric(horizontal: 16.gw),
        child: SingleChildScrollView(
          child: Column(
            children: [
              25.verticalSpace,
              // App Logo
              // Container(
              //   width: 80.gw,
              //   height: 80.gw,
              //   decoration: BoxDecoration(
              //     color: Color(0xFF4366DE),
              //     borderRadius: BorderRadius.circular(16.gr),
              //   ),
              //   child: Padding(
              //     padding: EdgeInsets.all(12.gw),
              //     child: SvgPicture.asset(
              //       Assets.appLogo,
              //       colorFilter: const ColorFilter.mode(Colors.white, BlendMode.srcIn),
              //     ),
              //   ),
              // ),
              // 8.verticalSpace,
              // App Name
              // FutureBuilder<String>(
              //   future: getIt<AppInfoService>().getAppName(),
              //   builder: (context, snapshot) {
              //     return Text(
              //       snapshot.data ?? kAppName,
              //       style: FontPalette.medium18,
              //     );
              //   },
              // ),
              // 4.verticalSpace,
              // Version

              // Menu Items
              BlocBuilder<AppInfoCubit, AppInfoState>(
                builder: (context, state) {
                  if (state.status == DataStatus.loading) {
                    return const AboutUsShimmer();
                  }
                  if (state.status == DataStatus.failed) {
                    return SizedBox.shrink();
                  }
                  return Container(
                    decoration: BoxDecoration(
                      color: context.theme.cardColor,
                      borderRadius: BorderRadius.circular(12.gr),
                    ),
                    child: Padding(
                      padding: EdgeInsets.symmetric(horizontal: 16.gw),
                      child: Column(
                        children: state.appInfoList.map((info) {
                          return _buildMenuItem(
                            context,
                            info.title,
                            hasBorder: state.appInfoList.indexOf(info) != state.appInfoList.length - 1,
                            onTap: () => Navigator.pushNamed(
                              context,
                              routeAppInfoContent,
                              arguments: info,
                            ),
                          );
                        }).toList(),
                      ),
                    ),
                  );
                },
              ),
              40.verticalSpace,
              FutureBuilder<String>(
                future: getIt<AppInfoService>().getAppVersion(),
                builder: (context, snapshot) {
                  return Text(
                    snapshot.data ?? kAppVersion,
                    style: FontPalette.normal14.copyWith(
                      color: context.colorTheme.textRegular,
                    ),
                  );
                },
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildMenuItem(
    BuildContext context,
    String title, {
    required VoidCallback onTap,
    required bool hasBorder,
  }) {
    return Bounceable(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.symmetric(vertical: 16.gh),
        decoration: BoxDecoration(
          border: hasBorder
              ? Border(
                  bottom: BorderSide(
                    color: Colors.grey.withNewOpacity(0.1),
                  ),
                )
              : null,
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              title,
              style: Theme.of(context).textTheme.titleMedium,
            ),
            Icon(
              Icons.chevron_right,
              color: Colors.grey,
              size: 24.gr,
            ),
          ],
        ),
      ),
    );
  }
}
