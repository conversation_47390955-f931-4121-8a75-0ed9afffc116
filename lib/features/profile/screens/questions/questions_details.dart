import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/profile/logic/help/help_cubit.dart';
import 'package:gp_stock_app/shared/app/utilities/easy_loading.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';
import 'package:gp_stock_app/shared/theme/font_pallette.dart';
import 'package:gp_stock_app/shared/theme/my_color_scheme.dart';
import 'package:gp_stock_app/shared/widgets/shadow_box.dart';
import 'package:gp_stock_app/shared/widgets/shimmer/shimmer_widget.dart';

import '../../widgets/settings/settings_appbar.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/shared/app/extension/color_extension.dart';

class QuestionsDetailsScreen extends StatelessWidget {
  const QuestionsDetailsScreen({super.key, required this.questionId});
  final int questionId;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      appBar: SettingsAppBar(title: 'feedBack'.tr()),
      body: SingleChildScrollView(
        child: _ContentSection(),
      ),
    );
  }
}

class _ContentSection extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.all(16.gr),
      decoration: BoxDecoration(
        color: context.theme.cardColor,
        borderRadius: BorderRadius.circular(8.gr),
      ),
      child: ShadowBox(
        child: BlocBuilder<HelpCubit, HelpState>(builder: (context, state) {
          if (state.helpDetailStatus == DataStatus.loading) {
            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                12.verticalSpace,
                Container(
                  width: 200.gw,
                  height: 20.gh,
                  decoration: BoxDecoration(
                    color: Colors.grey.withNewOpacity(0.2),
                    borderRadius: BorderRadius.circular(4.gr),
                  ),
                  child: ShimmerWidget(
                    height: 20.gh,
                  ),
                ),
                16.verticalSpace,
                Container(
                  width: double.infinity,
                  height: 100.gh,
                  decoration: BoxDecoration(
                    color: Colors.grey.withNewOpacity(0.2),
                    borderRadius: BorderRadius.circular(4.gr),
                  ),
                  child: ShimmerWidget(
                    height: 100.gh,
                  ),
                ),
                16.verticalSpace,
                Container(
                  width: double.infinity,
                  height: 80.gh,
                  decoration: BoxDecoration(
                    color: Colors.grey.withNewOpacity(0.2),
                    borderRadius: BorderRadius.circular(4.gr),
                  ),
                  child: ShimmerWidget(
                    height: 80.gh,
                  ),
                ),
              ],
            );
          }
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              12.verticalSpace,
              Text(
                state.selectedQuestion?.title ?? '',
                style: FontPalette.bold16.copyWith(
                  color: context.colorTheme.textPrimary,
                ),
              ),
              5.verticalSpace,
              Html(
                data: state.selectedQuestion?.content ?? '',
                style: {
                  "p": Style(
                    color: context.colorTheme.textPrimary,
                    fontSize: FontSize(14.gsp),
                  ),
                },
              ),
              _FeedbackSection(questionId: state.selectedQuestion?.id.toString() ?? ''),
            ],
          );
        }),
      ),
    );
  }
}

class _FeedbackSection extends StatefulWidget {
  final String questionId;

  const _FeedbackSection({required this.questionId});
  @override
  State<_FeedbackSection> createState() => _FeedbackSectionState();
}

class _FeedbackSectionState extends State<_FeedbackSection> {
  bool showFeedbackInput = false;
  final TextEditingController feedbackController = TextEditingController();

  @override
  void dispose() {
    feedbackController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      margin: EdgeInsets.symmetric(
        vertical: 20.gh,
      ),
      child: Column(
        children: [
          Text(
            'isHelpful'.tr(),
            style: FontPalette.bold13.copyWith(
              color: context.colorTheme.textPrimary,
            ),
          ),
          20.verticalSpace,
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              _FeedbackButton(
                onTap: () => context.read<HelpCubit>().markQuestionSolved(widget.questionId),
                icon: Icons.check_circle_outline,
                label: 'solved'.tr(),
                isPositive: true,
              ),
              40.horizontalSpace,
              _FeedbackButton(
                onTap: () => setState(() => showFeedbackInput = true),
                icon: Icons.cancel_outlined,
                label: 'unsolved'.tr(),
                isPositive: false,
              ),
            ],
          ),
          BlocListener<HelpCubit, HelpState>(
            listenWhen: (previous, current) =>
                current.updatingField == HelpField.feedback && previous.feedbackStatus != current.feedbackStatus,
            listener: (context, state) {
              if (state.feedbackStatus == DataStatus.loading) {
                GPEasyLoading.showLoading(message: 'loading'.tr());
              } else {
                GPEasyLoading.dismiss();
                if (state.feedbackStatus == DataStatus.success) {
                  GPEasyLoading.showToast('feedbackSubmitted'.tr());
                  setState(() {
                    showFeedbackInput = false;
                    feedbackController.clear();
                  });
                } else if (state.feedbackStatus == DataStatus.failed) {
                  GPEasyLoading.showToast(
                    state.error ?? 'somethingWentWrong'.tr(),
                  );
                }
              }
            },
            child: const SizedBox.shrink(),
          ),
          if (showFeedbackInput) ...[
            20.verticalSpace,
            Container(
              margin: EdgeInsets.symmetric(horizontal: 4.gw),
              decoration: BoxDecoration(
                color: context.theme.cardColor,
                borderRadius: BorderRadius.circular(8.gr),
                border: Border.all(
                  color: Colors.grey.withNewOpacity(0.2),
                ),
              ),
              child: TextField(
                controller: feedbackController,
                maxLines: 4,
                decoration: InputDecoration(
                  hintText: 'enterFeedback'.tr(),
                  contentPadding: EdgeInsets.all(12.gr),
                  border: InputBorder.none,
                  hintStyle: FontPalette.normal13.copyWith(
                    color: Colors.grey,
                  ),
                ),
                style: FontPalette.normal13.copyWith(
                  color: context.colorTheme.textPrimary,
                ),
              ),
            ),
            8.verticalSpace,
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                TextButton(
                  onPressed: () {
                    setState(() {
                      showFeedbackInput = false;
                      feedbackController.clear();
                    });
                  },
                  child: Text(
                    'cancel'.tr(),
                    style: FontPalette.normal13.copyWith(
                      color: Colors.grey,
                    ),
                  ),
                ),
                16.horizontalSpace,
                TextButton(
                  onPressed: () {
                    context.read<HelpCubit>().markQuestionUnsolved(
                          widget.questionId,
                          feedbackController.text,
                        );
                  },
                  child: Text(
                    'confirm'.tr(),
                    style: FontPalette.normal13.copyWith(
                      color: context.theme.primaryColor,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }
}

class _FeedbackButton extends StatelessWidget {
  final VoidCallback onTap;
  final IconData icon;
  final String label;
  final bool isPositive;

  const _FeedbackButton({
    required this.onTap,
    required this.icon,
    required this.label,
    required this.isPositive,
  });

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(20.gr),
        splashColor:
            isPositive ? const Color(0xFF4CAF50).withNewOpacity(0.1) : const Color(0xFFE57373).withNewOpacity(0.1),
        highlightColor:
            isPositive ? const Color(0xFF4CAF50).withNewOpacity(0.05) : const Color(0xFFE57373).withNewOpacity(0.05),
        child: Container(
          padding: EdgeInsets.symmetric(
            horizontal: 16.gw,
            vertical: 8.gh,
          ),
          decoration: BoxDecoration(
            border: Border.all(
              color: Colors.grey.withNewOpacity(0.2),
            ),
            borderRadius: BorderRadius.circular(20.gr),
          ),
          child: Row(
            children: [
              Icon(
                icon,
                color: isPositive ? const Color(0xFF4CAF50) : const Color(0xFFE57373),
                size: 20.gr,
              ),
              8.horizontalSpace,
              Text(
                label,
                style: FontPalette.normal12.copyWith(
                  color: context.colorTheme.textPrimary,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
