import 'dart:io';

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/shared/theme/my_color_scheme.dart';
import 'package:pinput/pinput.dart';

import '../../../../core/utils/validators.dart';
import '../../../../shared/constants/assets.dart';
import '../../../../shared/constants/enums.dart';
import '../../../../shared/models/dropdown/dropdown_value.dart';
import '../../../../shared/theme/color_pallette.dart';
import '../../../../shared/theme/font_pallette.dart';
import '../../../../shared/widgets/dropdown/common_dropdown.dart';
import '../../domain/models/auth_n/info/auth_n_info.dart';
import '../../logic/auth_n/auth_n_cubit.dart';
import '../../widgets/auth_n/card_upload.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';

class AuthFormFields extends StatelessWidget {
  final TextEditingController cardNameController;
  final TextEditingController cardNumberController;
  final TextEditingController withdrawPasswordController;
  final TextEditingController bankReservedMobileController;
  final TextEditingController bankCardNumberController;
  final AuthNInfo? authNInfo;
  final bool hideDocumentVerification;

  const AuthFormFields({
    super.key,
    required this.cardNameController,
    required this.cardNumberController,
    required this.withdrawPasswordController,
    required this.bankReservedMobileController,
    required this.bankCardNumberController,
    required this.authNInfo,
    this.hideDocumentVerification = true,
  });

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AuthNCubit, AuthNState>(
      buildWhen: (previous, current) =>
          previous.authNInfo != current.authNInfo ||
          previous.documentType != current.documentType ||
          previous.imageFileFront != current.imageFileFront ||
          previous.imageFileBack != current.imageFileBack,
      builder: (context, state) {
        final bool fieldsEnabled = (authNInfo?.idCard?.isEmpty ?? true) && (authNInfo?.realName?.isEmpty ?? true);
        final bool isIdCard = state.documentType?.code == CardType.id.name;
        final bool isPassport = state.documentType?.code == CardType.passport.name;
        final bool showDocuments = !hideDocumentVerification && fieldsEnabled && (isIdCard || isPassport);

        return Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            buildHeaderRow(context, state),
            12.verticalSpace,
            buildDocumentTypeDropdown(context, state, fieldsEnabled),
            20.verticalSpace,
            buildTextField('nameOnId'.tr(), cardNameController, fieldsEnabled),
            20.verticalSpace,
            buildTextField('idNumber'.tr(), cardNumberController, fieldsEnabled),
            20.verticalSpace,
            // Bank card number and reserved mobile fields are temporarily hidden
            // Uncomment when needed again
            /*
            buildTextField('cardNumber'.tr(), bankCardNumberController, fieldsEnabled, validator: (value) {
              if (value == null || value.isEmpty) {
                return 'pleaseEnterCardNumber'.tr();
              } else if (!Validators.isBankCard.hasMatch(value)) {
                return 'pleaseEnterValidCardNumber'.tr();
              }
              return null;
            }),
            20.verticalSpace,
            buildTextField('bankReservedMobile'.tr(), bankReservedMobileController, fieldsEnabled, validator: (value) {
              if (value == null || value.isEmpty) {
                return 'pleaseEnterBankReservedMobile'.tr();
              } else if (!Validators.isMobileValid(value)) {
                return 'pleaseEnterValidBankReservedMobile'.tr();
              }
              return null;
            }),
            */
            // 20.verticalSpace,
            if (fieldsEnabled) buildPasswordField(),
            if (showDocuments) ...[
              20.verticalSpace,
              if (isIdCard) buildIdCardUpload(context, state),
              if (isPassport) buildPassportUpload(context, state),
            ],
          ],
        );
      },
    );
  }

  Column buildPasswordField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        fieldTitle('withdrawPassword'.tr()),
        12.verticalSpace,
        Pinput(
          controller: withdrawPasswordController,
          length: 6,
          defaultPinTheme: PinTheme(
            width: 48,
            height: 48,
            textStyle: FontPalette.medium14,
            decoration: BoxDecoration(
              color: ColorPalette.textFieldBackgroundColor,
              borderRadius: BorderRadius.circular(10.gr),
              border: Border.all(color: Colors.transparent),
            ),
          ),
          focusedPinTheme: PinTheme(
            width: 48,
            height: 48,
            textStyle: FontPalette.medium14,
            decoration: BoxDecoration(
              color: ColorPalette.textFieldBackgroundColor,
              borderRadius: BorderRadius.circular(10.gr),
              border: Border.all(color: ColorPalette.primaryColor, width: 0.5),
            ),
          ),
          errorPinTheme: PinTheme(
            width: 48,
            height: 48,
            textStyle: FontPalette.medium14,
            decoration: BoxDecoration(
              color: ColorPalette.textFieldBackgroundColor,
              borderRadius: BorderRadius.circular(10.gr),
              border: Border.all(color: ColorPalette.redColor),
            ),
          ),
          errorTextStyle: FontPalette.medium11.copyWith(color: ColorPalette.redColor),
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'pleaseEnterWithdrawPassword'.tr();
            } else if (value.length < 6) {
              return 'pleaseEnterValidWithdrawPassword'.tr();
            }
            return null;
          },
        ),
      ],
    );
  }

  Widget buildHeaderRow(BuildContext context, AuthNState state) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        fieldTitle('documentType'.tr()),
        // if (statusText?.isNotEmpty == true)
        //   Container(
        //     margin: EdgeInsets.only(right: 12.gw),
        //     decoration: BoxDecoration(
        //       color: state.authNInfo?.toCertificateStatusColor().withValues(alpha: 0.7),
        //       borderRadius: BorderRadius.circular(10.gr),
        //     ),
        //     padding: EdgeInsets.all(8.gr),
        //     child: Text(
        //       statusText!,
        //       style: FontPalette.medium10.copyWith(color: Colors.white, fontFamily: 'Akzidenz-Grotesk'),
        //     ),
        //   ),
      ],
    );
  }

  Widget buildDocumentTypeDropdown(BuildContext context, AuthNState state, bool enabled) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        FormField<DropDownValue>(
          initialValue: null,
          validator: (value) {
            if (value == null) {
              return 'pleaseSelectDocumentType'.tr();
            }
            return null;
          },
          builder: (FormFieldState<DropDownValue> field) {
            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                CommonDropdown(
                  showSearchBox: false,
                  height: 48,
                  isEnabled: enabled,
                  hideSuffixIcon: !enabled,
                  selectedItem: state.documentType,
                  dropDownValue: [
                    DropDownValue(id: '1', value: CardType.id.translationKey, code: CardType.id.name),
                    // DropDownValue(id: '2', value: CardType.passport.translationKey, code: CardType.passport.name),
                  ],
                  onChanged: (value) {
                    context.read<AuthNCubit>().updateDocumentType(value);
                    field.didChange(value);
                  },
                  hintText: 'documentType'.tr(),
                ),
                if (field.hasError)
                  Padding(
                    padding: EdgeInsets.only(top: 8.gh, left: 8.gw),
                    child: Text(
                      field.errorText!,
                      style: FontPalette.medium11.copyWith(color: ColorPalette.redColor),
                    ),
                  ),
              ],
            );
          },
        ),
      ],
    );
  }

  Widget buildTextField(String label, TextEditingController controller, bool enabled,
      {String? Function(String?)? validator}) {
    return BlocBuilder<AuthNCubit, AuthNState>(
      buildWhen: (previous, current) => previous.documentType != current.documentType,
      builder: (context, state) {
        final isIdCard = state.documentType?.code == CardType.id.name;
        final isPassport = state.documentType?.code == CardType.passport.name;
        final inputDecoration = InputDecoration(
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(10.gr),
            borderSide: BorderSide(color: ColorPalette.primaryColor, width: 0.5),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(10.gr),
            borderSide: const BorderSide(color: Colors.transparent),
          ),
          errorBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(10.gr),
            borderSide: BorderSide(color: ColorPalette.redColor),
          ),
          disabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(10.gr),
            borderSide: BorderSide(color: Colors.transparent),
          ),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(10.gr),
            borderSide: BorderSide(color: Colors.transparent),
          ),
          fillColor: context.theme.scaffoldBackgroundColor,
          filled: true,
          errorStyle: FontPalette.medium11.copyWith(color: ColorPalette.redColor),
        );
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          spacing: 8.gh,
          children: [
            fieldTitle(label),
            if (label == 'nameOnId'.tr())
              TextFormField(
                controller: controller,
                enabled: enabled,
                decoration: inputDecoration,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'pleaseEnterYourName'.tr();
                  } else if (!Validators.isAuthName.hasMatch(value)) {
                    return 'pleaseEnterValidName'.tr();
                  }
                  return null;
                },
              )
            else if (label == 'idNumber'.tr())
              TextFormField(
                controller: controller,
                maxLength: 18,
                enabled: enabled,
                decoration: inputDecoration,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'pleaseEnterIdNumber'.tr();
                  } else if (isIdCard && !Validators.isIdCard.hasMatch(value)) {
                    return 'pleaseEnterValidIdNumber'.tr();
                  } else if (isPassport && !Validators.isPassport.hasMatch(value)) {
                    return 'pleaseEnterValidPassportNumber'.tr();
                  }
                  return null;
                },
              )
            else
              TextFormField(
                controller: controller,
                enabled: enabled,
                validator: validator,
                decoration: inputDecoration,
              ),
          ],
        );
      },
    );
  }

  Widget buildIdCardUpload(BuildContext context, AuthNState state) {
    return FormField<File>(
      validator: (value) {
        if (state.imageFileFront == null) {
          return 'pleaseUploadIdFront'.tr();
        }
        if (state.imageFileBack == null) {
          return 'pleaseUploadIdBack'.tr();
        }
        return null;
      },
      builder: (FormFieldState<File> field) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            fieldTitle('idFront'.tr()),
            12.verticalSpace,
            CardUpload(
              image: Assets.idFrontIcon,
              imageFile: state.imageFileFront,
              onTap: () => context.read<AuthNCubit>().getCardImage(type: CardTypeView.front),
            ),
            if (field.hasError && state.imageFileFront == null)
              Padding(
                padding: EdgeInsets.only(top: 8.gh),
                child: Text('pleaseUploadIdFront'.tr(), style: TextStyle(color: Colors.red, fontSize: 12.gsp)),
              ),
            20.verticalSpace,
            fieldTitle('idBack'.tr()),
            12.verticalSpace,
            CardUpload(
              image: Assets.idBackIcon,
              imageFile: state.imageFileBack,
              onTap: () => context.read<AuthNCubit>().getCardImage(type: CardTypeView.back),
            ),
            if (field.hasError && state.imageFileBack == null)
              Padding(
                padding: EdgeInsets.only(top: 8.gh),
                child: Text('pleaseUploadIdBack'.tr(), style: TextStyle(color: Colors.red, fontSize: 12.gsp)),
              ),
          ],
        );
      },
    );
  }

  Widget buildPassportUpload(BuildContext context, AuthNState state) {
    return FormField<File>(
      validator: (value) {
        if (state.imageFileFront == null) {
          return 'pleaseUploadPassport'.tr();
        }
        return null;
      },
      builder: (FormFieldState<File> field) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            fieldTitle('passportFront'.tr()),
            12.verticalSpace,
            CardUpload(
              image: Assets.passportIcon,
              imageFile: state.imageFileFront,
              onTap: () => context.read<AuthNCubit>().getCardImage(type: CardTypeView.front),
            ),
            if (field.hasError)
              Padding(
                padding: EdgeInsets.only(top: 8.gh),
                child: Text('pleaseUploadPassport'.tr(), style: TextStyle(color: Colors.red, fontSize: 12.gsp)),
              ),
          ],
        );
      },
    );
  }

  Widget fieldTitle(String title, {bool isRequired = true}) {
    return Row(
      children: [
        Text(title, style: FontPalette.medium14),
        if (isRequired) Text(' *', style: FontPalette.medium14.copyWith(color: ColorPalette.redColor)),
      ],
    );
  }
}
