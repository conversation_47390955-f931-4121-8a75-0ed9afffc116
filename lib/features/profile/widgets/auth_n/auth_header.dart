import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';

import '../../../../shared/constants/assets.dart';
import '../../../../shared/theme/font_pallette.dart';
import '../../../../shared/theme/my_color_scheme.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';

class AuthHeader extends StatelessWidget implements PreferredSizeWidget {
  const AuthHeader({super.key});

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      child: Stack(
        clipBehavior: Clip.none,
        children: [
          Container(
            height: 150.gh,
            width: double.infinity,
            decoration: BoxDecoration(
              color: context.theme.primaryColor,
              borderRadius: BorderRadius.only(
                bottomLeft: Radius.circular(20.gr),
                bottomRight: Radius.circular(20.gr),
              ),
            ),
            child: Padding(
              padding: EdgeInsets.only(
                top: 0.gh,
                left: 16.gw,
                right: 16.gw,
              ),
              child: Text(
                'authVerification'.tr(),
                style: FontPalette.semiBold30.copyWith(
                  color: Colors.white,
                ),
              ),
            ),
          ),
          Positioned(
            right: -20.gw,
            top: 0.gh,
            child: Image.asset(
              Assets.authHeaderIcon,
              width: 152.gw,
              height: 143.gh,
              fit: BoxFit.scaleDown,
            ),
          ),
        ],
      ),
    );
  }

  @override
  Size get preferredSize => Size.fromHeight(150.gh);
}
