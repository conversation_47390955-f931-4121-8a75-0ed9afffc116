import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/shared/logic/theme/theme_cubit.dart';
import 'package:gp_stock_app/shared/theme/my_color_scheme.dart';

import '../../../../shared/widgets/alert_dilaog/dialog_helper.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';

class ThemeDialog extends StatelessWidget {
  const ThemeDialog({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ThemeCubit, ThemeState>(
      builder: (context, state) {
        final themeMode = state.themeMode;
        return Padding(
          padding: EdgeInsets.only(bottom: MediaQuery.of(context).viewInsets.bottom),
          child: Stack(
            children: [
              Container(
                height: 320.gh,
                decoration: BoxDecoration(
                  color: context.theme.cardColor,
                  borderRadius: BorderRadius.circular(8.gr),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    DialogHeader(title: 'theme'.tr()),
                    Padding(
                      padding: EdgeInsets.symmetric(horizontal: 5.gw),
                      child: Padding(
                        padding: EdgeInsets.all(8.0.gr),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: [
                            10.verticalSpace,
                            ListTile(
                              onTap: () {
                                HapticFeedback.lightImpact();
                                context.read<ThemeCubit>().changeTheme(ThemeMode.system);
                                Navigator.pop(context);
                              },
                              contentPadding: EdgeInsets.zero,
                              leading: Icon(
                                Icons.settings,
                                color: context.theme.primaryColor,
                              ),
                              title: Text(
                                'systemTheme'.tr(),
                              ),
                              selected: themeMode == ThemeMode.system,
                              selectedColor: context.theme.primaryColor,
                            ),
                            ListTile(
                              onTap: () {
                                HapticFeedback.lightImpact();
                                context.read<ThemeCubit>().changeTheme(ThemeMode.light);
                                Navigator.pop(context);
                              },
                              contentPadding: EdgeInsets.zero,
                              leading: Icon(
                                Icons.wb_sunny,
                                color: context.theme.primaryColor,
                              ),
                              title: Text(
                                'lightTheme'.tr(),
                              ),
                              selected: themeMode == ThemeMode.light,
                              selectedColor: context.theme.primaryColor,
                            ),
                            ListTile(
                              onTap: () {
                                HapticFeedback.lightImpact();
                                context.read<ThemeCubit>().changeTheme(ThemeMode.dark);
                                Navigator.pop(context);
                              },
                              contentPadding: EdgeInsets.zero,
                              leading: Icon(
                                Icons.nights_stay,
                                color: context.theme.primaryColor,
                              ),
                              title: Text(
                                'darkTheme'.tr(),
                              ),
                              selected: themeMode == ThemeMode.dark,
                              selectedColor: context.theme.primaryColor,
                            ),
                            10.verticalSpace,
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
