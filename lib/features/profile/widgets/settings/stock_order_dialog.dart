import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';
import 'package:gp_stock_app/shared/logic/sort_color/sort_color_cubit.dart';

import '../../../../shared/constants/assets.dart';
import '../../../../shared/theme/my_color_scheme.dart';
import '../../../../shared/widgets/alert_dilaog/dialog_helper.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';

class StockOrderDialog extends StatelessWidget {
  const StockOrderDialog({super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(bottom: MediaQuery.of(context).viewInsets.bottom),
      child: Stack(
        children: [
          Container(
            height: 240,
            decoration: BoxDecoration(
              color: context.theme.cardColor,
              borderRadius: BorderRadius.circular(8.gr),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                DialogHeader(title: 'priceColor'.tr()),
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: 5.gw),
                  child: Padding(
                    padding: EdgeInsets.all(8.0.gr),
                    child: BlocSelector<SortColorCubit, SortColorState, MarketColor>(
                      selector: (state) => state.marketColor,
                      builder: (context, state) {
                        return Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: [
                            10.verticalSpace,
                            ListTile(
                              onTap: () {
                                context.read<SortColorCubit>().toggleMarketColor(MarketColor.redUpGreenDown);
                                Navigator.pop(context);
                              },
                              contentPadding: EdgeInsets.zero,
                              leading: SvgPicture.asset(
                                Assets.greenDownIcon,
                                height: 20.gr,
                                width: 20.gr,
                              ),
                              title: Text(
                                'redUpGreenDown'.tr(),
                              ),
                              selected: state == MarketColor.redUpGreenDown,
                              selectedColor: context.theme.primaryColor,
                            ),
                            ListTile(
                              contentPadding: EdgeInsets.zero,
                              onTap: () {
                                context.read<SortColorCubit>().toggleMarketColor(MarketColor.greenUpRedDown);
                                Navigator.pop(context);
                              },
                              title: Text(
                                'greenUpRedDown'.tr(),
                              ),
                              leading: SvgPicture.asset(
                                Assets.redDownIcon,
                                height: 20.gr,
                                width: 20.gr,
                              ),
                              selected: state == MarketColor.greenUpRedDown,
                              selectedColor: context.theme.primaryColor,
                            ),
                          ],
                        );
                      },
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
