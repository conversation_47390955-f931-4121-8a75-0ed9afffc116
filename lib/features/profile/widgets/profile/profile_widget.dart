import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_bounceable/flutter_bounceable.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/sign_in/logic/sign_in/sign_in_cubit.dart';
import 'package:gp_stock_app/shared/theme/color_pallette.dart';
import 'package:gp_stock_app/shared/theme/font_pallette.dart';
import 'package:gp_stock_app/shared/theme/my_color_scheme.dart';
import 'package:gp_stock_app/shared/widgets/flip_text.dart';

import '../../../../shared/constants/assets.dart';
import '../../../../shared/constants/enums.dart';
import '../../../../shared/routes/routes.dart';
import '../../../../shared/widgets/buttons/gr_button.dart';
import '../../logic/auth_n/auth_n_cubit.dart';
import '../../logic/profile/profile_cubit.dart';
import '../../screens/avatar/avatar_screen.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';

class ProfileDataWidget extends StatelessWidget {
  const ProfileDataWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<SignInCubit, SignInState>(
      builder: (context, signInState) {
        if (signInState.isSignedIn) {
          return BlocBuilder<ProfileCubit, ProfileState>(
            buildWhen: (previous, current) =>
                previous.userData != current.userData ||
                previous.updateStatus != current.updateStatus ||
                previous.userData?.avatar != current.userData?.avatar,
            builder: (context, profileState) {
              return Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  GestureDetector(
                    onTap: () => Navigator.of(context).push(
                      MaterialPageRoute(
                        builder: (_) => BlocProvider.value(
                          value: context.read<ProfileCubit>(),
                          child: const AvatarScreen(),
                        ),
                      ),
                    ),
                    child: Row(
                      children: [
                        if (profileState.updateStatus == DataStatus.loading &&
                            profileState.updatingField == ProfileUpdateField.avatar)
                          SizedBox(
                            width: 64.gw,
                            height: 64.gh,
                            child: Center(
                              child: SizedBox(
                                width: 30.gw,
                                height: 30.gh,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                  valueColor: AlwaysStoppedAnimation<Color>(
                                    context.theme.primaryColor,
                                  ),
                                ),
                              ),
                            ),
                          )
                        else
                          CircleAvatar(
                            radius: 30.5.gr,
                            backgroundColor: ColorPalette.primaryColor,
                            backgroundImage: profileState.userData?.avatar != null
                                ? AssetImage('assets/images/avatars/${profileState.userData!.avatar!}.png')
                                : null,
                            child: profileState.userData?.avatar == null
                                ? Icon(
                                    Icons.person_outline,
                                    color: Colors.white,
                                    size: 30.gw,
                                  )
                                : null,
                          ),
                        13.horizontalSpace,
                      ],
                    ),
                  ),
                  Expanded(
                    child: GestureDetector(
                      onTap: () => Navigator.pushNamed(context, routePersonalInfoEdit),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.start,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisAlignment: MainAxisAlignment.start,
                            children: [
                              Text(
                                profileState.userData?.nickname ?? '',
                                style: FontPalette.semiBold16.copyWith(
                                  color: Colors.white,
                                ),
                              ),
                              Text(
                                profileState.userData?.mobile ?? profileState.userData?.email ?? '',
                                style: FontPalette.normal14.copyWith(
                                  color: Colors.white,
                                ),
                              ),
                            ],
                          ),
                          8.horizontalSpace,
                          Row(
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              CertifiedWidget(),
                              8.horizontalSpace,
                              SvgPicture.asset(
                                Assets.getVipIcon(profileState.userData?.level ?? 0),
                                width: 30.gw,
                                height: 30.gh,
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              );
            },
          );
        }
        return Padding(
          padding: EdgeInsets.symmetric(horizontal: 16.gw),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'notLoggedIn'.tr(),
                    style: FontPalette.semiBold20.copyWith(color: Colors.white),
                  ),
                  Text(
                    'pleaseLoginToContinue'.tr(),
                    style: FontPalette.normal14.copyWith(
                      color: Colors.white.withValues(alpha: 0.5),
                    ),
                  ),
                ],
              ),
              const Spacer(),
              GrButton(
                width: 90,
                height: 30,
                text: 'login'.tr(),
                onTap: () {
                  Navigator.pushNamed(context, routeLogin);
                },
              ),
            ],
          ),
        );
      },
    );
  }
}

class BuildActionButton extends StatelessWidget {
  final String icon;
  final String title;
  final VoidCallback onTap;
  const BuildActionButton({
    super.key,
    required this.icon,
    required this.title,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Bounceable(
      onTap: onTap,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          SvgPicture.asset(
            width: 30.gw,
            height: 30.gh,
            icon,
          ),
          5.verticalSpace,
          Text(
            title,
            style: FontPalette.medium12,
          ),
        ],
      ),
    );
  }
}

class ActionOptionButton extends StatelessWidget {
  final String icon;
  final String title;
  final VoidCallback onTap;

  const ActionOptionButton({
    super.key,
    required this.icon,
    required this.title,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: 0.33.gsw,
      child: ElevatedButton(
        onPressed: onTap,
        style: ElevatedButton.styleFrom(
          backgroundColor: context.theme.primaryColor.withValues(alpha: 0.1),
          foregroundColor: context.theme.primaryColor,
          elevation: 0,
          padding: EdgeInsets.symmetric(
            horizontal: 16.gw,
            vertical: 16.gh,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12.gr),
            side: BorderSide(
              color: context.theme.primaryColor.withValues(alpha: 0.2),
            ),
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            SvgPicture.asset(
              icon,
              width: 32.gw,
              height: 32.gw,
            ),
            8.verticalSpace,
            Text(
              title,
              style: FontPalette.medium14,
            ),
          ],
        ),
      ),
    );
  }
}

class DiagonalBottomClipper extends CustomClipper<Path> {
  @override
  Path getClip(Size size) {
    final path = Path();
    path.lineTo(0, size.height - 30); // Start from top-left
    path.lineTo(size.width / 2, size.height); // Draw to middle point creating angle
    path.lineTo(size.width, size.height - 30); // Draw to bottom-right
    path.lineTo(size.width, 0); // Draw to top-right
    path.lineTo(0, 0); // Draw back to top-left
    path.close();
    return path;
  }

  @override
  bool shouldReclip(CustomClipper<Path> oldClipper) => false;
}

// Usage in your widget
class AngleBottomWidget extends StatelessWidget {
  final Widget child;
  const AngleBottomWidget({super.key, required this.child});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        clipBehavior: Clip.none,
        children: [
          ClipPath(
            clipper: DiagonalBottomClipper(),
            child: Container(
              height: 265.gh,
              decoration: BoxDecoration(
                color: context.theme.primaryColor,
                // Optional: Add gradient
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    context.theme.primaryColor,
                    context.theme.primaryColor,
                  ],
                ),
              ),
            ),
          ),
          Positioned(
            right: -20.gw,
            top: 44.gh,
            child: Image.asset(
              Assets.profileHeaderIcon,
              width: 152.gw,
              height: 143.gh,
              fit: BoxFit.scaleDown,
            ),
          ),
          child
        ],
      ),
    );
  }
}

class CertifiedWidget extends StatelessWidget {
  const CertifiedWidget({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AuthNCubit, AuthNState>(
      builder: (context, state) {
        if (state.getAuthNInfoStatus.isSuccess && state.authNInfo?.status == 1) {
          return Row(
            children: [
              SvgPicture.asset(
                Assets.certified,
                width: 18.gw,
                height: 18.gh,
              ),
              Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.only(
                    topRight: Radius.circular(14.gr),
                    bottomRight: Radius.circular(14.gr),
                  ),
                  border: Border(
                    right: BorderSide(width: 1, color: Colors.white),
                    top: BorderSide(width: 1, color: Colors.white),
                    bottom: BorderSide(width: 1, color: Colors.white),
                  ),
                ),
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 4.0),
                  child: Text(
                    'certified'.tr(),
                    style: FontPalette.normal8.copyWith(color: Colors.white),
                  ),
                ),
              ),
            ],
          );
        }
        return SizedBox.shrink();
      },
    );
  }
}

class HeaderItem extends StatelessWidget {
  final BuildContext context;
  final String title;
  final double value;
  final bool isCurrency;

  const HeaderItem({
    super.key,
    required this.context,
    required this.title,
    required this.value,
    this.isCurrency = false,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Text(
          title,
          style: FontPalette.medium12,
        ),
        SizedBox(height: 3.gh),
        SizedBox(
          height: 24.gh,
          child: FlipText(
            value,
            isCurrency: isCurrency,
            showCurrencyDropdown: isCurrency,
            style: FontPalette.extraBold14.copyWith(
              color: context.colorTheme.textPrimary,
              fontFamily: 'Akzidenz-Grotesk',
              fontWeight: FontWeight.w800,
            ),
          ),
        ),
      ],
    );
  }
}
