import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_bounceable/flutter_bounceable.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/account/logic/account/account_cubit.dart';

import '../../../../core/dependency_injection/injectable.dart';
import '../../../../core/utils/utils.dart';
import '../../../../shared/theme/font_pallette.dart';
import '../../../../shared/theme/my_color_scheme.dart';
import '../../../../shared/widgets/symbol/symbol_chip.dart';
import '../../../contract/logic/contract/contract_cubit.dart';
import '../../../contract/screens/contract_detail_screen.dart';
import '../../domain/models/account_summary/contract_summary_response.dart';
import '../data_field.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/shared/app/extension/color_extension.dart';

class ContractTile extends StatelessWidget {
  final ContractSummaryData contractSummary;
  const ContractTile({super.key, required this.contractSummary});

  @override
  Widget build(BuildContext context) {
    return Bounceable(
      onTap: () => Navigator.push(
        context,
        MaterialPageRoute(
          builder: (_) => MultiBlocProvider(
            providers: [
              BlocProvider(
                create: (context) => getIt<ContractCubit>(),
              ),
              BlocProvider(
                create: (context) => getIt<AccountCubit>(),
              ),
            ],
            child: ContractDetailScreen(contractSummary: contractSummary),
          ),
        ),
      ),
      child: Container(
        width: double.infinity,
        padding: EdgeInsets.all(5.gr),
        decoration: BoxDecoration(
          color: context.theme.cardColor,
          borderRadius: BorderRadius.circular(12.gr),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withNewOpacity(0.1),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Padding(
          padding: const EdgeInsets.all(8.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            spacing: 3.gh,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    spacing: 5.gh,
                    children: [
                      SymbolChip(
                          name: contractSummary.marketType ?? '', chipColor: context.theme.primaryColor),
                      SymbolChip(
                          name: contractSummary.currency?.toUpperCase() ?? '',
                          chipColor: context.colorTheme.textRegular),
                      SizedBox(
                        width: 220.gr,
                        child: Text(
                          getContractLabel(contractSummary),
                          style: FontPalette.semiBold12.copyWith(
                            overflow: TextOverflow.ellipsis,
                          ),
                          maxLines: 1,
                        ),
                      ),
                    ],
                  ),
                  Icon(Icons.arrow_forward_ios, size: 16.gr, color: context.theme.primaryColor),
                ],
              ),
              5.verticalSpace,
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                spacing: 10.gh,
                children: [
                  Expanded(child: DataField(label: 'availableBalance'.tr(), value: contractSummary.useAmount ?? 0)),
                  Expanded(child: DataField(label: 'frozenAmount'.tr(), value: contractSummary.freezePower ?? 0)),
                ],
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                spacing: 10.gh,
                children: [
                  Expanded(
                      child: DataField(
                    label: 'unrealizedPnl'.tr(),
                    value: contractSummary.todayWinAmount ?? 0,
                    color: contractSummary.todayWinAmount?.getValueColor(context),
                  )),
                  Expanded(
                      child: DataField(
                    label: '',
                    value: contractSummary.todayWinRate ?? 0,
                    suffix: '%',
                  )),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
