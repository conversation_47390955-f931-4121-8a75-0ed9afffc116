import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/core/theme/custom_text_theme.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/account/logic/withdraw/withdraw_cubit.dart';
import 'package:gp_stock_app/features/account/logic/withdraw/withdraw_state.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';
import 'package:gp_stock_app/shared/theme/font_pallette.dart';
import 'package:gp_stock_app/shared/theme/my_color_scheme.dart';
import 'package:gp_stock_app/shared/widgets/pagination/common_refresher.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

import '../domain/models/withdrawal_records/withdrawal_record.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';

class WithdrawalRecordsScreen extends StatefulWidget {
  const WithdrawalRecordsScreen({super.key});

  @override
  State<WithdrawalRecordsScreen> createState() => _WithdrawalRecordsScreenState();
}

class _WithdrawalRecordsScreenState extends State<WithdrawalRecordsScreen> {
  final RefreshController _refreshController = RefreshController();

  @override
  void initState() {
    super.initState();
    _loadRecords();
  }

  void _loadRecords() {
    context.read<WithdrawalCubit>().getWithdrawalRecords();
  }

  void _onRefresh() async {
    await context.read<WithdrawalCubit>().getWithdrawalRecords();
    _refreshController.refreshCompleted();
  }

  void _onLoading() async {
    final state = context.read<WithdrawalCubit>().state;
    if ((state.records?.length ?? 0) >= (state.total ?? 0)) {
      _refreshController.loadNoData();
    } else {
      await context.read<WithdrawalCubit>().getWithdrawalRecords(isLoadMore: true);
      _refreshController.loadComplete();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('withdrawal_records'.tr()),
        backgroundColor: context.theme.cardColor,
        centerTitle: true,
      ),
      body: BlocBuilder<WithdrawalCubit, WithdrawalState>(
        builder: (context, state) {
          if (state.recordsFetchStatus == DataStatus.loading && (state.records?.isEmpty ?? true)) {
            return const Center(child: CircularProgressIndicator.adaptive());
          }

          if (state.recordsFetchStatus == DataStatus.failed) {
            return Center(child: Text(state.error ?? 'failedToLoad'.tr()));
          }

          if (state.records?.isEmpty ?? true) {
            return CommonRefresher(
              controller: _refreshController,
              enablePullDown: true,
              onRefresh: _onRefresh,
              child: ListView(
                physics: const AlwaysScrollableScrollPhysics(),
                children: [
                  SizedBox(
                    height: 0.7.gsh,
                    child: Center(
                      child: Text('noMoreData'.tr(),
                          style: FontPalette.normal14.copyWith(
                            color: context.colorTheme.textRegular,
                          )),
                    ),
                  ),
                ],
              ),
            );
          }

          return CommonRefresher(
            controller: _refreshController,
            enablePullDown: true,
            enablePullUp: true,
            onRefresh: _onRefresh,
            onLoading: _onLoading,
            child: ListView.builder(
              padding: EdgeInsets.all(16.gr),
              itemCount: state.records?.length ?? 0,
              itemBuilder: (context, index) {
                final record = state.records![index];
                return _WithdrawalRecordCard(record: record);
              },
            ),
          );
        },
      ),
    );
  }
}

class _WithdrawalRecordCard extends StatelessWidget {
  final WithdrawalRecord record;

  const _WithdrawalRecordCard({required this.record});

  String _getStatusText(int status) {
    return switch (status) {
      0 => 'reviewPending'.tr(),
      1 => 'withdrawal_status_processing'.tr(),
      2 => 'withdrawal_status_success'.tr(),
      3 => 'withdrawal_status_failed'.tr(),
      4 => 'withdrawal_status_cancelled'.tr(),
      _ => 'unknown'.tr(),
    };
  }

  Color _getStatusColor(int status, BuildContext context) {
    return switch (status) {
      0 => context.colorTheme.textRegular,
      1 => context.theme.primaryColor,
      2 => context.colorTheme.stockGreen,
      3 => context.colorTheme.stockRed,
      4 => context.colorTheme.textPrimary,
      _ => context.colorTheme.textRegular,
    };
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(bottom: 16.gh),
      decoration: BoxDecoration(
        color: context.theme.cardColor,
        borderRadius: BorderRadius.circular(12.gr),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Padding(
        padding: EdgeInsets.all(16.gr),
        child: Column(
          children: [
            _buildInfoRow('withdrawChannel'.tr(), record.channelName ?? '', context),
            _buildInfoRow('withdrawAmount'.tr(), record.chargeAmount?.toStringAsFixed(2) ?? "0.00", context),
            _buildInfoRow('service_charge'.tr(), record.chargeFee?.toStringAsFixed(2) ?? "0.00", context),
            _buildInfoRow('actualAmount'.tr(), record.realAmount?.toStringAsFixed(2) ?? "0.00", context,
                valueColor: Colors.blue),
            _buildInfoRow('chargeCurrency'.tr(), record.chargeCurrency ?? 'CNY', context),
            _buildInfoRow('chargeDesc'.tr(), record.chargeDesc?.isNotEmpty == true ? record.chargeDesc! : '-', context),
            _buildInfoRow('status'.tr(), _getStatusText(record.status ?? 0), context,
                valueColor: _getStatusColor(record.status ?? 0, context)),
            _buildInfoRow('createTime'.tr(), record.createTime ?? '', context),
            _buildInfoRow('orderNumber'.tr(), record.orderNo ?? '', context,
                valueColor: context.colorTheme.stockRed),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value, BuildContext context, {Color? valueColor}) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 6.gh),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: FontPalette.normal14.copyWith(
              color: context.colorTheme.textRegular,
            ),
          ),
          Text(
            value,
            style: FontPalette.semiBold14.copyWith(
              color: valueColor ?? context.theme.primaryColor,
              fontFamily: 'Akzidenz-Grotesk',
            ),
          ),
        ],
      ),
    );
  }
}
