import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/account/logic/pay_order/pay_order_cubit.dart';
import 'package:gp_stock_app/features/account/logic/pay_order/pay_order_state.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';
import 'package:gp_stock_app/shared/theme/font_pallette.dart';
import 'package:gp_stock_app/shared/theme/my_color_scheme.dart';
import 'package:gp_stock_app/shared/widgets/pagination/common_refresher.dart';
import 'package:gp_stock_app/shared/widgets/shadow_box.dart';
import 'package:gp_stock_app/shared/widgets/shimmer/shimmer_widget.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

import '../../domain/models/pay_order/pay_order_response.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';

class PayOrderScreen extends StatefulWidget {
  const PayOrderScreen({super.key});

  @override
  State<PayOrderScreen> createState() => _PayOrderScreenState();
}

class _PayOrderScreenState extends State<PayOrderScreen> {
  final RefreshController _refreshController = RefreshController();

  @override
  void initState() {
    super.initState();
    _loadPayOrders();
  }

  void _loadPayOrders() {
    context.read<PayOrderCubit>().getPayOrders();
  }

  void _onRefresh() async {
    await context.read<PayOrderCubit>().getPayOrders();
    _refreshController.refreshCompleted();
  }

  void _onLoading() async {
    final state = context.read<PayOrderCubit>().state;
    if ((state.payOrders?.length ?? 0) >= (state.total ?? 0)) {
      _refreshController.loadNoData();
    } else {
      await context.read<PayOrderCubit>().getPayOrders(isLoadMore: true);
      _refreshController.loadComplete();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('paymentOrders'.tr()),
        centerTitle: true,
        // actions: [
        //   IconButton(
        //     icon: const Icon(Icons.filter_list),
        //     onPressed: () => _showFilterDialog(context),
        //   ),
        // ],
      ),
      body: BlocBuilder<PayOrderCubit, PayOrderState>(
        builder: (context, state) {
          if (state.payOrdersFetchStatus == DataStatus.loading && (state.payOrders?.isEmpty ?? true)) {
            return ListView.builder(
              padding: EdgeInsets.all(16.gr),
              itemCount: 5, // Show 5 shimmer items
              itemBuilder: (context, index) => const _PayOrderShimmer(),
            );
          }

          if (state.payOrdersFetchStatus == DataStatus.failed) {
            return Center(child: Text(state.error ?? 'failedToLoad'.tr()));
          }

          if (state.payOrders?.isEmpty ?? true) {
            return CommonRefresher(
              controller: _refreshController,
              enablePullDown: true,
              onRefresh: _onRefresh,
              child: ListView(
                physics: const AlwaysScrollableScrollPhysics(),
                children: [
                  SizedBox(
                    height: 0.7.gsh,
                    child: Center(
                      child: Text('noMoreData'.tr(),
                          style: FontPalette.normal14.copyWith(
                            color: context.colorTheme.textRegular,
                          )),
                    ),
                  ),
                ],
              ),
            );
          }

          return CommonRefresher(
            controller: _refreshController,
            enablePullDown: true,
            enablePullUp: true,
            onRefresh: _onRefresh,
            onLoading: _onLoading,
            child: ListView.separated(
              padding: EdgeInsets.all(16.gr),
              itemCount: state.payOrders?.length ?? 0,
              separatorBuilder: (context, index) => SizedBox(height: 8),
              itemBuilder: (context, index) {
                final record = state.payOrders![index];
                return _PayOrderCard(record: record);
              },
            ),
          );
        },
      ),
    );
  }

  // void _showFilterDialog(BuildContext context) {
  //   DateTime? startDate;
  //   DateTime? endDate;

  //   showDialog(
  //     context: context,
  //     builder: (context) => AlertDialog(
  //       title: Text('filterByDate'.tr()),
  //       content: Column(
  //         mainAxisSize: MainAxisSize.min,
  //         children: [
  //           ListTile(
  //             title: Text('startDate'.tr()),
  //             subtitle: Text(startDate != null ? DateFormat('yyyy-MM-dd').format(startDate!) : 'selectDate'.tr()),
  //             onTap: () async {
  //               final picked = await showDatePicker(
  //                 context: context,
  //                 initialDate: DateTime.now(),
  //                 firstDate: DateTime(2020),
  //                 lastDate: DateTime.now(),
  //               );
  //               if (picked != null) {
  //                 setState(() {
  //                   startDate = picked;
  //                 });
  //               }
  //             },
  //           ),
  //           ListTile(
  //             title: Text('endDate'.tr()),
  //             subtitle: Text(endDate != null ? DateFormat('yyyy-MM-dd').format(endDate!) : 'selectDate'.tr()),
  //             onTap: () async {
  //               final picked = await showDatePicker(
  //                 context: context,
  //                 initialDate: DateTime.now(),
  //                 firstDate: DateTime(2020),
  //                 lastDate: DateTime.now(),
  //               );
  //               if (picked != null) {
  //                 setState(() {
  //                   endDate = picked;
  //                 });
  //               }
  //             },
  //           ),
  //         ],
  //       ),
  //       actions: [
  //         TextButton(
  //           onPressed: () {
  //             Navigator.of(context).pop();
  //             context.read<PayOrderCubit>().clearFilters();
  //           },
  //           child: Text('clear'.tr()),
  //         ),
  //         TextButton(
  //           onPressed: () {
  //             Navigator.of(context).pop();
  //             if (startDate != null || endDate != null) {
  //               context.read<PayOrderCubit>().updateDateFilter(
  //                     startDate: startDate != null ? DateFormat('yyyy-MM-dd').format(startDate!) : null,
  //                     endDate: endDate != null ? DateFormat('yyyy-MM-dd').format(endDate!) : null,
  //                   );
  //             }
  //           },
  //           child: Text('apply'.tr()),
  //         ),
  //       ],
  //     ),
  //   );
  // }
}

class _PayOrderCard extends StatelessWidget {
  final PayOrderRecord record;

  const _PayOrderCard({required this.record});

  @override
  Widget build(BuildContext context) {
    return ShadowBox(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'depositChannel'.tr(),
                style: FontPalette.normal14.copyWith(
                  color: context.colorTheme.textRegular,
                ),
              ),
              Text(
                '${record.channelName ?? 0}',
                style: FontPalette.bold16.copyWith(
                  color: context.theme.primaryColor,
                ),
              ),
            ],
          ),
          SizedBox(height: 8.gr),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'orderAmount'.tr(),
                style: FontPalette.normal14.copyWith(
                  color: context.colorTheme.textRegular,
                ),
              ),
              Text(
                '${record.orderAmount ?? 0}',
                style: FontPalette.bold16.copyWith(
                  color: context.theme.primaryColor,
                ),
              ),
            ],
          ),
          SizedBox(height: 8.gr),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'status'.tr(),
                style: FontPalette.normal14.copyWith(
                  color: context.colorTheme.textRegular,
                ),
              ),
              _buildStatusText(context, record.status ?? 0),
            ],
          ),
          SizedBox(height: 8.gr),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'createTime'.tr(),
                style: FontPalette.normal14.copyWith(
                  color: context.colorTheme.textRegular,
                ),
              ),
              Text(
                record.createTime ?? '',
                style: FontPalette.normal14,
              ),
            ],
          ),
          SizedBox(height: 8.gr),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'orderNumber'.tr(),
                style: FontPalette.normal14.copyWith(
                  color: context.colorTheme.textRegular,
                ),
              ),
              Text(
                record.orderNo ?? '',
                style: FontPalette.normal14.copyWith(
                  color: context.colorTheme.stockRed,
                  fontFamily: 'Akzidenz-Grotesk',
                ),
              ),
            ],
          ),
          SizedBox(height: 8.gr),
        ],
      ),
    );
  }

  Widget _buildStatusText(BuildContext context, int status) {
    String statusText;
    Color statusColor;

    switch (status) {
      case 0:
        statusText = 'waiting_for_payment'.tr();
        statusColor = Colors.orange;
        break;
      case 1:
        statusText = 'transaction_success'.tr();
        statusColor = Colors.green;
        break;
      case 2:
        statusText = 'transaction_failed'.tr();
        statusColor = Colors.red;
        break;
      default:
        statusText = 'unknown'.tr();
        statusColor = Colors.grey;
    }

    return Text(
      statusText,
      style: FontPalette.normal14.copyWith(color: statusColor),
    );
  }
}

// Add this new widget at the end of the file, before the closing brace
class _PayOrderShimmer extends StatelessWidget {
  const _PayOrderShimmer();

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(bottom: 8.gr),
      child: ShimmerWidget(
        child: ShadowBox(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Container(
                    width: 100.gw,
                    height: 14.gh,
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(4.gr),
                    ),
                  ),
                  Container(
                    width: 80.gw,
                    height: 16.gh,
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(4.gr),
                    ),
                  ),
                ],
              ),
              SizedBox(height: 8.gr),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Container(
                    width: 80.gw,
                    height: 14.gh,
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(4.gr),
                    ),
                  ),
                  Container(
                    width: 70.gw,
                    height: 14.gh,
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(4.gr),
                    ),
                  ),
                ],
              ),
              SizedBox(height: 8.gr),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Container(
                    width: 90.gw,
                    height: 14.gh,
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(4.gr),
                    ),
                  ),
                  Container(
                    width: 120.gw,
                    height: 14.gh,
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(4.gr),
                    ),
                  ),
                ],
              ),
              SizedBox(height: 8.gr),
            ],
          ),
        ),
      ),
    );
  }
}
