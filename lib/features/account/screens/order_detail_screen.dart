import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/core/theme/custom_text_theme.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/core/utils/utils.dart';
import 'package:gp_stock_app/features/account/domain/models/account_summary/contract_summary_response.dart';
import 'package:gp_stock_app/features/account/domain/models/order/order_response.dart';
import 'package:gp_stock_app/features/account/logic/account/account_cubit.dart';
import 'package:gp_stock_app/features/account/widgets/table_empty.dart';
import 'package:gp_stock_app/shared/app/extension/helper.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';
import 'package:gp_stock_app/shared/theme/font_pallette.dart';
import 'package:gp_stock_app/shared/theme/my_color_scheme.dart';
import 'package:gp_stock_app/shared/widgets/app_header.dart';
import 'package:gp_stock_app/shared/widgets/shadow_box.dart';
import 'package:gp_stock_app/shared/widgets/symbol/symbol_chip.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';

class OrderDetailScreen extends StatefulWidget {
  final int orderId;

  const OrderDetailScreen({
    super.key,
    required this.orderId,
  });

  @override
  State<OrderDetailScreen> createState() => _OrderDetailScreenState();
}

class _OrderDetailScreenState extends State<OrderDetailScreen> with AppHeaderMixin {
  @override
  void initState() {
    super.initState();
    Helper.afterInit(() {
      context.read<AccountCubit>().getOrderById(widget.orderId);
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('commissionDetails'.tr()),
      ),
      body: Column(
        children: [
          Expanded(
            child: BlocBuilder<AccountCubit, AccountState>(
              buildWhen: (previous, current) =>
                  previous.orderDetailFetchStatus != current.orderDetailFetchStatus ||
                  previous.orderDetail != current.orderDetail,
              builder: (context, state) {
                if (state.orderDetailFetchStatus == DataStatus.loading) {
                  return const Center(child: CircularProgressIndicator.adaptive());
                } else if (state.orderDetailFetchStatus == DataStatus.failed) {
                  return TableEmptyWidget();
                } else if (state.orderDetail != null) {
                  return _buildOrderDetails(context, state.orderDetail!);
                } else {
                  return TableEmptyWidget();
                }
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildOrderDetails(BuildContext context, OrderRecord order) {
    // Determine buy/sell direction
    final isContract = order.contractId != null && order.contractId != 0;
    return SingleChildScrollView(
      padding: EdgeInsets.all(16.gr),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Order status header
          ShadowBox(
            child: Column(
              spacing: 10,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  spacing: 8,
                  children: [
                    Text(
                      '${order.symbolName}',
                      style: FontPalette.bold14.copyWith(color: context.colorTheme.textRegular),
                    ),
                    SymbolChip(name: order.market ?? ''),
                    Text(
                      '(${order.symbol})',
                      style: FontPalette.normal14.copyWith(color: context.colorTheme.textRegular),
                    ),
                  ],
                ),
                _buildDetailRow(
                  'accountType'.tr(),
                  isContract
                      ? getContractLabel(ContractSummaryData(
                          id: order.contractId,
                          type: order.contractType,
                          periodType: order.periodType,
                          marketType: order.market,
                          multiple: order.multiple,
                        ))
                      : "spotTrading".tr(),
                ),
                _buildDetailRow(
                  'status'.tr(),
                  EntrustStatus.fromValueByValue(order.status ?? 0).label.tr(),
                  valueColor: EntrustStatus.fromValueByValue(order.status ?? 0).color,
                ),
                if (order.dealTime != null) ...[
                  _buildDetailRow(
                    'entrustTime'.tr(),
                    order.tradeTime ?? '',
                  ),
                ],
                if (order.tradeTime != null) ...[
                  _buildDetailRow(
                    'transactionTime'.tr(),
                    order.dealTime ?? '',
                  ),
                ],
              ],
            ),
          ),
          20.verticalSpace,
          // Stock information
          ShadowBox(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              spacing: 10,
              children: [
                _buildDetailRow(
                  'direction'.tr(),
                  TradeDirection.fromValue(order.direction ?? 0).translationKey.tr(),
                  valueColor:
                      TradeDirection.getColor(context, tradeDirection: TradeDirection.fromValue(order.direction ?? 0)),
                  valueFontWeight: FontWeight.bold,
                ),
                _buildDetailRow(
                  'orderType'.tr(),
                  TradeType.fromValue(order.type ?? 0).translationKey.tr(),
                  valueColor: TradeType.getColor(context, tradeType: TradeType.fromValue(order.type ?? 0)),
                  valueFontWeight: FontWeight.bold,
                ),
                _buildDetailRow(
                  'orderPrice'.tr(),
                  order.tradePrice?.toString(),
                  valueFontWeight: FontWeight.bold,
                ),
                _buildDetailRow(
                  'dealPrice'.tr(),
                  order.dealPrice.toString(),
                  valueFontWeight: FontWeight.bold,
                ),
                _buildDetailRow(
                  'total'.tr(),
                  order.tradeNum?.toString(),
                  valueFontWeight: FontWeight.bold,
                ),
                _buildDetailRow(
                  'filled'.tr(),
                  order.dealNum?.toString(),
                  valueFontWeight: FontWeight.bold,
                ),
                _buildDetailRow(
                  'totalProfitAndLoss'.tr(),
                  order.winAmount?.toString(),
                  valueFontWeight: FontWeight.bold,
                ),
                _buildDetailRow(
                  'transaction_amount'.tr(),
                  order.transactionAmount?.toString(),
                  valueFontWeight: FontWeight.bold,
                ),
                _buildDetailRow(
                  'service_charge'.tr(),
                  order.tradeFee?.toString(),
                  valueFontWeight: FontWeight.bold,
                ),
              ],
            ),
          ),

          20.verticalSpace,
        ],
      ),
    );
  }

  Widget _buildDetailRow(
    String label,
    String? value, {
    Color? valueColor,
    FontWeight? valueFontWeight,
  }) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: FontPalette.normal14.copyWith(
            color: context.colorTheme.textRegular,
          ),
        ),
        Text(
          value ?? '',
          style: FontPalette.normal14.copyWith(
            color: valueColor ?? context.theme.primaryColor,
            fontWeight: valueFontWeight ?? FontWeight.normal,
            fontFamily: 'Akzidenz-Grotesk',
          ),
        ),
      ],
    );
  }
}
