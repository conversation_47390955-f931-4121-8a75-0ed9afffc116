import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/core/theme/custom_text_theme.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/account/logic/deposit/deposit_cubit.dart';
import 'package:gp_stock_app/features/account/logic/deposit/deposit_state.dart';
import 'package:gp_stock_app/shared/app/extension/color_extension.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';
import 'package:gp_stock_app/shared/theme/font_pallette.dart';
import 'package:gp_stock_app/shared/theme/my_color_scheme.dart';
import 'package:gp_stock_app/shared/widgets/pagination/common_refresher.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

import '../domain/models/deposit_records/deposit_record.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';

class DepositRecordsScreen extends StatefulWidget {
  const DepositRecordsScreen({super.key});

  @override
  State<DepositRecordsScreen> createState() => _DepositRecordsScreenState();
}

class _DepositRecordsScreenState extends State<DepositRecordsScreen> {
  final RefreshController _refreshController = RefreshController();

  @override
  void initState() {
    super.initState();
    _loadRecords();
  }

  void _loadRecords() {
    context.read<DepositCubit>().getDepositRecords();
  }

  void _onRefresh() async {
    await context.read<DepositCubit>().getDepositRecords();
    _refreshController.refreshCompleted();
  }

  void _onLoading() async {
    final state = context.read<DepositCubit>().state;
    if ((state.depositRecords?.length ?? 0) >= (state.depositTotal ?? 0)) {
      _refreshController.loadNoData();
    } else {
      await context.read<DepositCubit>().getDepositRecords(isLoadMore: true);
      _refreshController.loadComplete();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('depositRecords'.tr()),
        centerTitle: true,
      ),
      body: BlocBuilder<DepositCubit, DepositState>(
        builder: (context, state) {
          if (state.depositRecordsFetchStatus == DataStatus.loading && (state.depositRecords?.isEmpty ?? true)) {
            return const Center(child: CircularProgressIndicator.adaptive());
          }

          if (state.depositRecordsFetchStatus == DataStatus.failed) {
            return Center(child: Text(state.error ?? 'failedToLoad'.tr()));
          }

          if (state.depositRecords?.isEmpty ?? true) {
            return CommonRefresher(
              controller: _refreshController,
              enablePullDown: true,
              onRefresh: _onRefresh,
              child: ListView(
                physics: const AlwaysScrollableScrollPhysics(),
                children: [
                  SizedBox(
                    height: 0.7.gsh,
                    child: Center(
                      child: Text('noMoreData'.tr(),
                          style: FontPalette.normal14.copyWith(
                            color: context.colorTheme.textRegular,
                          )),
                    ),
                  ),
                ],
              ),
            );
          }

          return CommonRefresher(
            controller: _refreshController,
            enablePullDown: true,
            enablePullUp: true,
            onRefresh: _onRefresh,
            onLoading: _onLoading,
            child: ListView.builder(
              padding: EdgeInsets.all(8.gr),
              itemCount: state.depositRecords?.length ?? 0,
              itemBuilder: (context, index) {
                final record = state.depositRecords![index];
                return _DepositRecordCard(record: record);
              },
            ),
          );
        },
      ),
    );
  }
}

class _DepositRecordCard extends StatelessWidget {
  final DepositRecord record;

  const _DepositRecordCard({required this.record});

  String _getStatusText(int status) {
    return switch (status) {
      0 => '-'.tr(),
      1 => 'pending'.tr(), // '待审核'
      2 => 'toBeCredited'.tr(), // '待入账' (new key)
      3 => 'toBeRefunded'.tr(), // '待退款' (new key)
      4 => 'credited'.tr(), // '已到账'
      5 => 'refunded'.tr(), // '已退款'
      6 => 'depositFailed'.tr(), // '充值失败'
      _ => 'unknown'.tr(),
    };
  }

  Color _getStatusColor(int status, BuildContext context) {
    return switch (status) {
      0 => context.colorTheme.textRegular, // Default color
      1 => context.colorTheme.textRegular, // 'text'
      2 => context.theme.primaryColor, // 'primary'
      3 => context.theme.primaryColor, // 'special'
      4 => context.colorTheme.stockGreen, // 'green'
      5 => context.colorTheme.stockRed, // 'red'
      6 => context.colorTheme.stockRed, // 'red'
      _ => context.colorTheme.textRegular,
    };
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(bottom: 12.gh),
      decoration: BoxDecoration(
        color: context.theme.cardColor,
        borderRadius: BorderRadius.circular(12.gr),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withNewOpacity(0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Padding(
        padding: EdgeInsets.all(12.gr),
        child: Column(
          children: [
            _buildInfoRow('rechargeAmount'.tr(), record.chargeAmount?.toStringAsFixed(2) ?? "0.00", context),
            _buildInfoRow(
              'status'.tr(),
              _getStatusText(record.status ?? 0),
              context,
              valueColor: _getStatusColor(record.status ?? 0, context),
            ),
            _buildInfoRow('createTime'.tr(), record.createTime ?? '', context),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value, BuildContext context, {Color? valueColor}) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 6.gh),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: FontPalette.normal14.copyWith(
              color: context.colorTheme.textRegular,
            ),
          ),
          Text(
            value,
            style: FontPalette.semiBold14.copyWith(
              color: valueColor ?? context.theme.primaryColor,
              fontFamily: 'Akzidenz-Grotesk',
            ),
          ),
        ],
      ),
    );
  }
}
