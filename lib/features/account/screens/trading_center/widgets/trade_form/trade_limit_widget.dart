import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/core/theme/custom_text_theme.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/account/logic/trading/trading_cubit.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';
import 'package:gp_stock_app/shared/theme/font_pallette.dart';
import 'package:gp_stock_app/shared/theme/my_color_scheme.dart';
import 'package:gp_stock_app/shared/widgets/counter_textfield.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';

class TradeLimitWidget extends StatefulWidget {
  /// A widget that handles trade limit price input and display.
  ///
  /// This widget provides:
  /// - Real-time price display for market orders
  /// - Limit price input with increment/decrement controls
  /// - Automatic price rounding to 2 decimal places
  /// - Focus management for text input
  const TradeLimitWidget({super.key});

  @override
  State<TradeLimitWidget> createState() => _TradeLimitWidgetState();
}

class _TradeLimitWidgetState extends State<TradeLimitWidget> {
  final TextEditingController _limitController = TextEditingController();
  final FocusNode _focusNode = FocusNode();

  /// The number of decimal places to round the price to
  final int _decimalPlaces = 3;

  /// The multiplier used for rounding calculations
  final int _roundingMultiplier = 100;

  @override
  void dispose() {
    _limitController.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Expanded(
      child: BlocSelector<TradingCubit, TradingState,
          ({PriceType priceType, double limit, double latestPrice, bool isIndexTrading})>(
        selector: (state) => (
          priceType: state.priceType,
          limit: state.limit,
          latestPrice: state.stockInfoConstant?.latestPrice ?? 0,
          isIndexTrading: state.isIndexTrading
        ),
        builder: (context, state) {
          // Update controller text if limit changed externally
          if (_limitController.text != state.limit.toStringAsFixed(_decimalPlaces) && !_focusNode.hasFocus) {
            _limitController.text = state.limit.toStringAsFixed(_decimalPlaces);
          }

          // Show real-time price display for market orders
          if (state.priceType == PriceType.market) {
            return _buildRealTimePriceLabelDisplay();
          }

          // Show limit price input for limit orders
          return CounterTextfield(
            onDecrementPressed: context.read<TradingCubit>().canDecrementLimit()
                ? () => context.read<TradingCubit>().decrementLimit()
                : null,
            onIncrementPressed: () => context.read<TradingCubit>().incrementLimit(),
            controller: _limitController,
            focusNode: _focusNode,
            onFocusChanged: () {
              final currentValue = double.tryParse(_limitController.text) ?? state.limit;
              _updateLimit(currentValue, state.latestPrice);
            },
          );
        },
      ),
    );
  }

  /// Updates the limit price in the trading state
  void _updateLimit(double price, double latestPrice) {
    final roundedPrice = _roundPrice(price);
    final finalPrice = roundedPrice == 0 ? latestPrice : roundedPrice;
    _limitController.text = finalPrice.toStringAsFixed(_decimalPlaces);
    context.read<TradingCubit>().setLimit(finalPrice);
  }

  /// Rounds the given price to 2 decimal places
  double _roundPrice(double price) {
    return (price * _roundingMultiplier).round() / _roundingMultiplier;
  }

  /// Builds the real-time price display container
  Widget _buildRealTimePriceLabelDisplay() {
    return Container(
      height: 35.gh,
      padding: EdgeInsets.symmetric(horizontal: 12.gw),
      decoration: BoxDecoration(
        color: context.theme.scaffoldBackgroundColor,
        borderRadius: BorderRadius.circular(5.gr),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            'realTimePrice'.tr(),
            style: FontPalette.normal13.copyWith(
              color: context.colorTheme.textPrimary,
            ),
          ),
        ],
      ),
    );
  }
}
