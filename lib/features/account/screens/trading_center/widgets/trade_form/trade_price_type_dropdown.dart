import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/core/theme/custom_text_theme.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/account/logic/trading/trading_cubit.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';
import 'package:gp_stock_app/shared/models/dropdown/dropdown_value.dart';
import 'package:gp_stock_app/shared/theme/font_pallette.dart';
import 'package:gp_stock_app/shared/theme/my_color_scheme.dart';
import 'package:gp_stock_app/shared/widgets/dropdown/common_dropdown.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';

/// A dropdown widget for selecting trade price types (Market Order or Limit Order) in the trading interface.
///
/// This widget manages the selection between different price types for trading operations:
/// * Market Order: Executes the trade at the current market price
/// * Limit Order: Executes the trade at a specified price or better (not available for index trading)
///
/// The widget integrates with [TradingCubit] for state management and uses [CommonDropdown]
/// for the UI implementation. It automatically disables limit order selection during index trading
/// and updates the trading state through the cubit when the selection changes.
///
/// Key features:
/// * Dynamically shows/hides limit order option based on index trading status
/// * Automatically disables selection during index trading
class TradePriceTypeDropdown extends StatelessWidget {
  /// Creates a TradePriceTypeDropdown widget.
  ///
  /// The widget doesn't require any parameters as it internally manages its state
  /// through the [TradingCubit] using the BlocSelector pattern.
  const TradePriceTypeDropdown({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocSelector<TradingCubit, TradingState, ({PriceType priceType, bool isIndexTrading})>(
      selector: (state) => (priceType: state.priceType, isIndexTrading: state.isIndexTrading),
      builder: (context, state) {
        List<DropDownValue> dropDownValue = [
          DropDownValue(id: '1', value: 'marketOrder'.tr()),
          if (!state.isIndexTrading) DropDownValue(id: '2', value: 'limitOrder'.tr()),
        ];
        return Expanded(
          child: CommonDropdown(
            height: 35.gh,
            showSearchBox: false,
            isEnabled: !state.isIndexTrading,
            selectedItem: DropDownValue(id: '${state.priceType.value}', value: state.priceType.tr),
            dropDownValue: dropDownValue,
            onChanged: (value) {
              context.read<TradingCubit>().setPriceType(PriceType.values[int.parse(value.id) - 1]);
            },
            hintText: '',
            borderRadius: 5.gr,
            textStyle: FontPalette.normal13.copyWith(color: context.colorTheme.textPrimary),
          ),
        );
      },
    );
  }
}
