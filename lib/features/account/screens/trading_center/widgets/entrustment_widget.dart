import 'package:animated_flip_counter/animated_flip_counter.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/core/theme/custom_text_theme.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/account/domain/models/order/order_response.dart';
import 'package:gp_stock_app/features/account/logic/trading/trading_cubit.dart';
import 'package:gp_stock_app/features/account/screens/trading_center/widgets/trade_form/trade_bottomsheet_orders.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';
import 'package:gp_stock_app/shared/theme/font_pallette.dart';
import 'package:gp_stock_app/shared/theme/my_color_scheme.dart';

import '../../../../../shared/theme/color_pallette.dart';
import '../../../../../shared/widgets/symbol/symbol_chip.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';

class EntrustmentWidget extends StatelessWidget {
  const EntrustmentWidget({
    super.key,
    required this.data,
    this.showRevokeButton = true,
  });

  final OrderRecord data;
  final bool showRevokeButton;
  @override
  Widget build(BuildContext context) {
    final entrustStatus = EntrustStatus.fromValueByValue(data.status ?? 1);
    return Padding(
      padding: EdgeInsets.symmetric(
        horizontal: 4.gw,
      ),
      child: Container(
        color: Colors.transparent,
        child: Padding(
          padding: const EdgeInsets.symmetric(vertical: 8.0),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              // Name and Symbol Column
              Expanded(
                flex: 3,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  spacing: 5.gh,
                  children: [
                    Text(
                      data.symbolName ?? 'N/A',
                      style: FontPalette.semiBold14,
                    ),
                    Row(
                      spacing: 5.gh,
                      children: [
                        SymbolChip(
                            name: data.market?.substring(0, 2) ?? 'N/A',
                            chipColor: context.theme.primaryColor),
                        Text(
                          data.symbol ?? 'N/A',
                          style: FontPalette.semiBold12.copyWith(
                            color: context.colorTheme.textRegular,
                            fontFamily: 'Akzidenz-Grotesk',
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),

              Expanded(
                  flex: 2,
                  child: AnimatedFlipCounter(
                    fractionDigits: 2,
                    decimalSeparator: '.',
                    thousandSeparator: ',',
                    textStyle: FontPalette.bold14.copyWith(
                      color: ColorPalette.primaryColor,
                      fontFamily: 'Akzidenz-Grotesk',
                    ),
                    value: data.tradePrice ?? 0.00,
                  )),

              Expanded(
                flex: 2,
                child: Column(
                  children: [
                    Text(
                      data.dealNum?.toNumeric ?? 'N/A',
                      textAlign: TextAlign.center,
                      style: FontPalette.semiBold14
                          .copyWith(fontFamily: 'Akzidenz-Grotesk', color: context.colorTheme.textRegular),
                    ),
                    Text(
                      data.tradeNum?.toNumeric ?? 'N/A',
                      textAlign: TextAlign.center,
                      style: FontPalette.semiBold14
                          .copyWith(fontFamily: 'Akzidenz-Grotesk', color: context.colorTheme.textRegular),
                    ),
                  ],
                ),
              ),

              Expanded(
                flex: 2,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    Text(
                      '${TradeDirection.fromValue(data.direction ?? 0).translationKey.tr()}${TradeType.fromValue(data.tradeType ?? 0).translationKey.tr()}',
                      textAlign: TextAlign.center,
                      style: FontPalette.normal12.copyWith(
                          color: TradeDirection.getColor(context,
                              tradeDirection: TradeDirection.fromValue(data.direction ?? 0))),
                    ),
                    Text(
                      entrustStatus.label.tr(),
                      textAlign: TextAlign.right,
                      style: FontPalette.semiBold10.copyWith(
                        color: entrustStatus.color,
                        // fontFamily: 'Akzidenz-Grotesk',
                        fontSize: 10.gh,
                      ),
                    ),
                  ],
                ),
              ),
              8.horizontalSpace,
              if (showRevokeButton)
                Expanded(
                  flex: 2,
                  child: TextButton(
                    onPressed: () {
                      showDialog(
                        context: context,
                        builder: (_) => BlocProvider.value(
                          value: context.read<TradingCubit>(),
                          child: CancelOrderDialog(orderId: data.id!),
                        ),
                      );
                    },
                    child: Text(
                      'revoke'.tr(),
                      style: FontPalette.bold12.copyWith(
                        color: context.colorTheme.stockRed,
                      ),
                    ),
                  ),
                )
            ],
          ),
        ),
      ),
    );
  }
}
