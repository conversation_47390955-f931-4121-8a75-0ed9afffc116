import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/core/theme/custom_text_theme.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/account/domain/models/quotes/company_news/company_news_response.dart';
import 'package:gp_stock_app/features/account/logic/trading/trading_cubit.dart';
import 'package:gp_stock_app/features/account/widgets/table_empty.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';
import 'package:gp_stock_app/shared/models/instrument/instrument.dart';
import 'package:gp_stock_app/shared/theme/font_pallette.dart';
import 'package:gp_stock_app/shared/theme/my_color_scheme.dart';

import '../../../../../../shared/routes/routes.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';

class NewsListItem extends StatelessWidget {
  const NewsListItem({
    super.key,
    required this.article,
  });

  final ArticleRecord article;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        Navigator.pushNamed(
          context,
          routeCompanyNewsDetails,
          arguments: article.id,
        );
      },
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              article.articleTitle ?? "xxx",
              style: FontPalette.normal14.copyWith(color: context.colorTheme.textPrimary),
            ),
            const SizedBox(height: 6),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  article.articleDate ?? "",
                  style: SecFontPalette.normal12.copyWith(color: context.colorTheme.textRegular),
                ),
                Text(
                  article.articleAuth ?? "",
                  style: SecFontPalette.normal12.copyWith(color: context.colorTheme.textRegular),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

class QuotesNewsList extends StatefulWidget {
  final Instrument instrument;

  const QuotesNewsList({
    super.key,
    required this.instrument,
  });

  @override
  State<QuotesNewsList> createState() => _QuotesNewsListState();
}

class _QuotesNewsListState extends State<QuotesNewsList> {
  final controller = ScrollController();

  @override
  void initState() {
    super.initState();
    context.read<TradingCubit>().getCompanyNews(widget.instrument);
    controller.addListener(_onScroll);
  }

  void _onScroll() {
    if (controller.position.pixels == controller.position.maxScrollExtent) {
      context.read<TradingCubit>().getCompanyNews(widget.instrument);
    }
  }

  @override
  void dispose() {
    controller.removeListener(_onScroll);
    controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.fromLTRB(0, 20, 0, 0),
      child: BlocSelector<TradingCubit, TradingState, ({DataStatus status, CompanyNewsResponse? response})>(
        selector: (state) => (response: state.companyNewsResponse, status: state.companyNewsFetchStatus),
        builder: (context, state) {
          if (state.status.isLoading && state.response == null) {
            return Center(child: CircularProgressIndicator.adaptive());
          }

          if (state.status.isFailed || state.response == null) {
            return Center(child: TableEmptyWidget());
          }
          final news = state.response?.data?.records ?? [];
          if (news.isEmpty) {
            return Center(child: TableEmptyWidget());
          }

          return RefreshIndicator(
            onRefresh: () async {
              await context.read<TradingCubit>().getCompanyNews(widget.instrument, isRefresh: true);
            },
            child: ListView.builder(
                controller: controller,
                shrinkWrap: true,
                itemCount: news.length + 1,
                itemBuilder: (context, index) {
                  if (index == news.length) {
                    if (state.response?.data?.hasNext ?? false) {
                      return Padding(
                        padding: EdgeInsets.all(8.gr),
                        child: Center(
                          child: CircularProgressIndicator.adaptive(),
                        ),
                      );
                    }
                    return const SizedBox.shrink();
                  }
                  return NewsListItem(article: news[index]);
                }),
          );
        },
      ),
    );
    //   child: PaginatedManagerList<ArticleRecord>(
    //     shrinkWrap: true,
    //     paginationManager: paginationManager,
    //     itemBuilder: (context, index, items) => NewsListItem(article: items[index]),
    //     scrollThreshold: 0.8,
    //     showRefreshIndicator: true,
    //     emptyItemsWidget: Center(child: TableEmptyWidget()),
    //     retryText: "Try Again",
    //     errorTextStyle: SecFontPalette.bold12,
    //     initialLoadingWidget: Center(child: const CircularProgressIndicator.adaptive()),
    //     loadingPaginationWidget: const Center(child: CircularProgressIndicator.adaptive()),
    //     whenErrMessageFromPagination: (message) {
    //       Helper.showFlutterToast(message);
    //     },
    //   ),
    // );
  }
}
