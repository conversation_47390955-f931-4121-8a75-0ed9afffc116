import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/core/theme/custom_text_theme.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/account/logic/trading/trading_cubit.dart';
import 'package:gp_stock_app/features/market/domain/models/broker_queue/broker_queue.dart';
import 'package:gp_stock_app/shared/theme/color_pallette.dart';
import 'package:gp_stock_app/shared/theme/font_pallette.dart';
import 'package:gp_stock_app/shared/theme/my_color_scheme.dart';

class BrokerQueueView extends StatefulWidget {
  const BrokerQueueView({super.key});

  @override
  State<BrokerQueueView> createState() => _BrokerQueueViewState();
}

class _BrokerQueueViewState extends State<BrokerQueueView> {
  final counts = [10, 20, 40];
  int count = 10;
  @override
  Widget build(BuildContext context) {
    return BlocSelector<TradingCubit, TradingState, ({List<BrokerQueueLevel> buy, List<BrokerQueueLevel> sell})>(
      selector: (state) => (buy: state.brokerQueue?.buy ?? [], sell: state.brokerQueue?.sell ?? []),
      builder: (context, state) {
        if (state.buy.isEmpty || state.sell.isEmpty) return const SizedBox.shrink();
        return Container(
          padding: EdgeInsets.all(16.gr),
          color: context.theme.cardColor,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Expanded(
                    child: Text(
                      'sellBroker'.tr(),
                      style: FontPalette.semiBold14,
                    ),
                  ),
                  Expanded(
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          'buyBroker'.tr(),
                          style: FontPalette.semiBold14,
                        ),
                        PopupMenuButton<int>(
                          position: PopupMenuPosition.under,
                          constraints: BoxConstraints(maxHeight: 150.gh, maxWidth: 40.gw),
                          padding: EdgeInsets.zero,
                          color: context.theme.cardColor,
                          onSelected: (int item) {
                            setState(() {
                              count = item;
                            });
                          },
                          child: Container(
                            padding: EdgeInsets.symmetric(horizontal: 0.gw, vertical: 4.gh),
                            decoration: BoxDecoration(
                              color: context.theme.cardColor,
                              borderRadius: BorderRadius.circular(4.gr),
                            ),
                            child: Container(
                              width: 18.gw,
                              height: 18.gh,
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(4.gr),
                                color: context.theme.cardColor,
                                border: Border.all(width: .3, color: context.colorTheme.textPrimary),
                              ),
                              child: Center(
                                child: Text(
                                  count.toString(),
                                  style: FontPalette.semiBold12.copyWith(
                                      fontFamily: 'Akzidenz-Grotesk', color: context.colorTheme.textRegular),
                                ),
                              ),
                            ),
                          ),
                          itemBuilder: (context) => counts
                              .map(
                                (n) => PopupMenuItem<int>(
                                  value: n,
                                  child: Container(
                                    width: 18.gw,
                                    height: 18.gh,
                                    decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(4.gr),
                                      color: context.theme.cardColor,
                                      border: Border.all(width: .3, color: context.colorTheme.textPrimary),
                                    ),
                                    child: Center(
                                      child: Text(
                                        n.toString(),
                                        style: FontPalette.semiBold12.copyWith(
                                            fontFamily: 'Akzidenz-Grotesk', color: context.colorTheme.textRegular),
                                      ),
                                    ),
                                  ),
                                ),
                              )
                              .toList(),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              8.verticalSpace,
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Expanded(child: _BrokerList(data: state.sell, isBuy: false, count: count)),
                  Expanded(child: _BrokerList(data: state.buy, isBuy: true, count: count)),
                ],
              ),
            ],
          ),
        );
      },
    );
  }
}

class _BrokerList extends StatelessWidget {
  const _BrokerList({required this.data, required this.isBuy, required this.count});

  final List<BrokerQueueLevel> data;
  final bool isBuy;
  final int count;

  @override
  Widget build(BuildContext context) {
    int remainingCount = count;
    int currentIndex = 0;

    return Column(
      children: List.generate(
        data.length,
        (index) {
          if (remainingCount <= 0 || currentIndex >= data.length) return const SizedBox.shrink();

          final currentData = data[currentIndex];
          final itemCount = currentData.list.length + 1; // +1 for the header

          if (itemCount <= remainingCount) {
            remainingCount -= itemCount;
            currentIndex++;
            return _BrokerItem(
              data: currentData,
              isBuy: isBuy,
              maxBrokerCount: itemCount - 1, // Subtract header
            );
          } else if (remainingCount > 0) {
            final widget = _BrokerItem(
              data: currentData,
              isBuy: isBuy,
              maxBrokerCount: remainingCount - 1, // Subtract header
            );
            remainingCount = 0;
            currentIndex++;
            return widget;
          }

          return const SizedBox.shrink();
        },
      ).where((widget) => widget != const SizedBox.shrink()).toList(),
    );
  }
}

class _BrokerItem extends StatelessWidget {
  const _BrokerItem({
    required this.data,
    required this.isBuy,
    required this.maxBrokerCount,
  });

  final BrokerQueueLevel data;
  final bool isBuy;
  final int maxBrokerCount;

  @override
  Widget build(BuildContext context) {
    final textColor = isBuy ? ColorPalette.redColor : ColorPalette.greenColor;
    bool isDark = Theme.of(context).brightness == Brightness.dark;
    final itemBg = (isBuy
        ? (isDark ? Colors.red[300]?.withAlpha(20) : Colors.red[50])
        : (isDark ? Colors.green[300]?.withAlpha(20) : Colors.green[50]));

    final serialBg = isBuy ? ColorPalette.redColor : ColorPalette.greenColor;
    final brokerBg = isBuy
        ? isDark
            ? Colors.red[500]?.withAlpha(20)
            : Color(0xffF0C8C7)
        : isDark
            ? Colors.green[500]?.withAlpha(20)
            : Color(0xffC9E9D7);

    return Column(
      children: [
        Container(
          height: 24.gh,
          color: brokerBg,
          padding: EdgeInsets.symmetric(horizontal: 12.gw),
          child: Row(
            children: [
              Container(
                height: 18.gh,
                width: 18.gw,
                decoration: BoxDecoration(
                  color: serialBg,
                  borderRadius: BorderRadius.circular(2.gr),
                ),
                child: Center(
                  child: Text(
                    data.level.toString(),
                    style: FontPalette.semiBold12.copyWith(
                      color: Colors.white,
                      fontFamily: 'Akzidenz-Grotesk',
                    ),
                  ),
                ),
              ),
              Expanded(
                child: Align(
                  alignment: Alignment.centerRight,
                  child: Text(
                    data.price.toString(),
                    style: FontPalette.semiBold12.copyWith(
                      color: textColor,
                      fontFamily: 'Akzidenz-Grotesk',
                    ),
                  ),
                ),
              ),
              Expanded(
                flex: 2,
                child: Align(
                  alignment: Alignment.centerRight,
                  child: Text(
                    '(${data.list.length})',
                    style: FontPalette.semiBold12.copyWith(fontFamily: 'Akzidenz-Grotesk'),
                  ),
                ),
              ),
            ],
          ),
        ),
        ...data.list.take(maxBrokerCount).map(
              (broker) => Container(
                height: 24.gh,
                padding: EdgeInsets.symmetric(horizontal: 12.gw),
                color: itemBg,
                child: Row(
                  children: [
                    Text(
                      broker.brokerId,
                      style: FontPalette.normal12,
                    ),
                    10.horizontalSpace,
                    Expanded(
                      child: Text(
                        broker.brokerName,
                        style: FontPalette.normal12,
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                ),
              ),
            ),
      ],
    );
  }
}
