import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:gp_stock_app/core/theme/custom_text_theme.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/core/utils/utils.dart';
import 'package:gp_stock_app/features/account/domain/models/quotes/dist_flow/dist_flow_response.dart';
import 'package:gp_stock_app/shared/app/extension/color_extension.dart';
import 'package:gp_stock_app/shared/theme/font_pallette.dart';

import '../../../../../../shared/theme/my_color_scheme.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';

class DistBar<PERSON>hart extends StatelessWidget {
  final DistFlowData data;
  const DistBarChart({super.key, required this.data});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            _buildBarChartSection(
              context: context,
              title: 'in'.tr(), // Inflow
              total: _getTotalInflow(),
              values: [
                data.dist?.inData?.maxAmount ?? 0,
                data.dist?.inData?.midAmount ?? 0,
                data.dist?.inData?.minAmount ?? 0,
              ],
              color: Colors.red,
            ),
            SizedBox(width: 5.gw),
            _buildBarChartSection(
              context: context,
              title: 'out'.tr(), // Outflow
              total: _getTotalOutflow(),
              values: [
                data.dist?.outData?.maxAmount ?? 0,
                data.dist?.outData?.midAmount ?? 0,
                data.dist?.outData?.minAmount ?? 0,
              ],
              color: Colors.green,
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildBarChartSection({
    required BuildContext context,
    required String title,
    required double total,
    required List<double> values,
    required Color color,
  }) {
    final width = 160.0.gw;
    final height = 200.0.gh;

    // Find the maximum value to calculate relative heights
    final maxValue = values.reduce((curr, next) => curr > next ? curr : next);
    final barWidth = 30.0.gw;
    final barSpacing = (width - (barWidth * 3)) / 5;

    // Labels for the bars
    final labels = [
      'large_order'.tr(),
      'medium_order'.tr(),
      'small_order'.tr(),
    ];
    final isRight = color == Colors.green;
    return Column(
      children: [
        SizedBox(
          height: height,
          child: Column(
            children: [
              // Bar values
              Expanded(
                child: Padding(
                  padding: EdgeInsets.only(left: isRight ? 0 : 0),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: List.generate(values.length, (index) {
                      final value = values[index];
                      // Calculate relative height (minimum 10 for visibility)
                      final barHeight =
                          maxValue > 0 ? ((value / maxValue) * (height - 60.gh)).clamp(10.0, height - 60.gh) : 10.0;

                      return Padding(
                        padding: EdgeInsets.symmetric(horizontal: barSpacing / 2),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.end,
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            // Value label
                            Text(
                              formatLargeNumber(value, context.locale.languageCode),
                              style: SecFontPalette.normal12.copyWith(
                                color: context.colorTheme.textPrimary,
                              ),
                            ),
                            SizedBox(height: 5.gh),
                            // Bar
                            Container(
                              width: barWidth,
                              height: barHeight,
                              decoration: BoxDecoration(
                                color: color.withNewOpacity(1 - (index * 0.2)),
                                borderRadius: BorderRadius.circular(2),
                              ),
                            ),
                            SizedBox(height: 5.gh),
                            Text(
                              labels[index],
                              style: SecFontPalette.normal12.copyWith(
                                color: context.colorTheme.textPrimary,
                              ),
                            ),
                          ],
                        ),
                      );
                    }),
                  ),
                ),
              ),
            ],
          ),
        ),
        SizedBox(height: 10.gh),
        // Title and total
        Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              title,
              style: SecFontPalette.normal16.copyWith(
                color: context.colorTheme.textRegular,
              ),
            ),
            SizedBox(width: 4.gw),
            Text(
              formatLargeNumber(total, context.locale.languageCode),
              style: SecFontPalette.bold16.copyWith(
                color: title == '流入' ? Colors.red[800] : Colors.green[800],
              ),
            ),
          ],
        ),
      ],
    );
  }

  double _getTotalInflow() {
    return ((data.dist?.inData?.maxAmount ?? 0) +
        (data.dist?.inData?.midAmount ?? 0) +
        (data.dist?.inData?.minAmount ?? 0));
  }

  double _getTotalOutflow() {
    return ((data.dist?.outData?.maxAmount ?? 0) +
        (data.dist?.outData?.midAmount ?? 0) +
        (data.dist?.outData?.minAmount ?? 0));
  }
}
