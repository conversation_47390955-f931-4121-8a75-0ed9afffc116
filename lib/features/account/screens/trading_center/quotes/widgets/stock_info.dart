import 'package:country_flags/country_flags.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/core/theme/custom_text_theme.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/core/utils/utils.dart';
import 'package:gp_stock_app/features/account/domain/models/quotes/market_status/market_status_response.dart';
import 'package:gp_stock_app/features/account/logic/trading/trading_cubit.dart';
import 'package:gp_stock_app/features/account/screens/trading_center/quotes/widgets/stock_info_item.dart';
import 'package:gp_stock_app/features/account/utils/trading_utils.dart';
import 'package:gp_stock_app/features/market/domain/models/stock_reponse/stock_info_response.dart';
import 'package:gp_stock_app/shared/app/extension/time_zone.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';
import 'package:gp_stock_app/shared/theme/color_pallette.dart';
import 'package:gp_stock_app/shared/theme/font_pallette.dart';
import 'package:gp_stock_app/shared/widgets/flip_text.dart';
import 'package:gp_stock_app/shared/widgets/shimmer/shimmer_widget.dart';

import '../../../../../../shared/theme/my_color_scheme.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';

class StockInfoHeader extends StatelessWidget {
  const StockInfoHeader({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.fromLTRB(13, 14, 13, 0),
      decoration: BoxDecoration(
        color: context.theme.cardColor,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildMarketStatus(context),
          const SizedBox(height: 4),
          _buildStockDetails(context),
        ],
      ),
    );
  }

  /// Builds the market status and timestamp row.
  Widget _buildMarketStatus(BuildContext context) {
    return BlocSelector<TradingCubit, TradingState, (DataStatus, MarketStatusResponse?, StockInfoData?)>(
      selector: (state) => (state.marketStatusFetchStatus, state.marketStatus, state.stockInfo),
      builder: (context, state) {
        if (state.$1 == DataStatus.loading) {
          return const ShimmerWidget(height: 20);
        }
        if (state.$1 == DataStatus.failed || state.$2 == null) {
          return const Center(child: Icon(Icons.error));
        }

        final status = state.$2!.data?.statusStr ?? "";
        final time = _formatTimestamp(response: state.$2, stock: state.$3);
        return Row(
          children: [
            Text(
              status,
              style: FontPalette.normal12.copyWith(color: context.colorTheme.textRegular),
            ),
            const SizedBox(width: 8),
            Text(
              time,
              style: FontPalette.normal12.copyWith(color: context.colorTheme.textRegular),
            ),
          ],
        );
      },
    );
  }

  /// Builds the stock price, gain, and name section.
  Widget _buildStockDetails(BuildContext context) {
    return BlocSelector<TradingCubit, TradingState, (DataStatus, StockInfoData?)>(
      selector: (state) => (state.stockInfoStatus, state.stockInfo),
      builder: (context, state) {
        if (state.$1 == DataStatus.loading) {
          return ShimmerWidget(height: 80.gh);
        }
        if (state.$1 == DataStatus.failed || state.$2 == null) {
          return const Center(child: Icon(Icons.error));
        }

        final stock = state.$2!;
        final chg = TradingUtils.formatNumber(stock.chg);
        final gain = TradingUtils.formatPercentage((stock.gain ?? 0) * 100, isPercent: false, decimalPlaces: 3);
        final name = stock.name ?? "--";
        final currency = stock.currency ?? "--";
        final gainColor = (stock.gain ?? 0).getValueColor(context);

        return Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Expanded(
              flex: 4,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      FlipText(
                        stock.latestPrice ?? 0,
                        style: SecFontPalette.bold20.copyWith(color: gainColor),
                        fractionDigits: 3,
                      ),
                      Icon(
                        stock.gain != null && stock.gain! >= 0 ? Icons.arrow_drop_up : Icons.arrow_drop_down,
                        color: gainColor,
                      ),
                    ],
                  ),
                  Text(
                    "${TradingUtils.getSign(stock.gain)}$chg  ${TradingUtils.getSign(stock.gain)}$gain %",
                    style: SecFontPalette.bold13.copyWith(color: gainColor),
                  ),
                ],
              ),
            ),
            Expanded(
              flex: 6,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end, // Align content to the right
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Flexible(
                    child: Text(
                      name,
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                      style: SecFontPalette.bold14.copyWith(color: gainColor),
                    ),
                  ),
                  const SizedBox(width: 8),
                  ClipRRect(
                    borderRadius: BorderRadius.circular(2),
                    child: CountryFlag.fromCountryCode(
                      currency.substring(0, 2),
                      width: 20,
                      height: 15,
                    ),
                  ),
                ],
              ),
            ),
          ],
        );
      },
    );
  }
}

class StockInfoBody extends StatefulWidget {
  const StockInfoBody({super.key});

  @override
  State<StockInfoBody> createState() => _StockInfoBodyState();
}

class _StockInfoBodyState extends State<StockInfoBody> {
  bool _isExpanded = false;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(14),
      decoration: BoxDecoration(
        color: context.theme.cardColor,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildStockInfo(context),
          _buildExpandableSection(context),
          const SizedBox(height: 10),
          _buildExpandButton(),
        ],
      ),
    );
  }

  /// Builds the main stock info section (always visible).
  Widget _buildStockInfo(BuildContext context) {
    return BlocSelector<TradingCubit, TradingState, (DataStatus, StockInfoData?)>(
      selector: (state) => (state.stockInfoStatus, state.stockInfo),
      builder: (context, state) {
        if (state.$1 == DataStatus.loading) {
          return ShimmerWidget(height: 100.gh);
        }
        if (state.$1 == DataStatus.failed || state.$2 == null) {
          return const Center(child: Icon(Icons.error));
        }

        final stock = state.$2!;
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildInfoRow(
              context,
              left: StockInfoItem("high_price".tr(), stock.high ?? 0.0, Colors.red, isPrice: true, precision: 3),
              right: StockInfoItem("low_price".tr(), stock.low ?? 0.0, ColorPalette.greenColor,
                  isPrice: true, precision: 3),
            ),
            const SizedBox(height: 12),
            _buildInfoRow(
              context,
              left: StockInfoItem("opening_price".tr(), stock.open ?? 0.0, ColorPalette.greenColor,
                  isPrice: true, precision: 3),
              right: StockInfoItem("prev_close".tr(), stock.close ?? 0.0, Colors.grey, isPrice: true, precision: 3),
            ),
            const SizedBox(height: 12),
            _buildInfoRow(
              context,
              left: StockInfoItem("turnover_amount".tr(), stock.amount ?? 0.0, Colors.grey, isVolume: true),
              right: StockInfoItem("volume".tr(), stock.volume ?? 0, Colors.grey, isVolume: true, isBold: true),
            ),
          ],
        );
      },
    );
  }

  /// Builds the expandable section with additional stock details.
  Widget _buildExpandableSection(BuildContext context) {
    return BlocSelector<TradingCubit, TradingState, StockInfoData?>(
      selector: (state) => state.stockInfo,
      builder: (context, stock) {
        if (stock == null) return const SizedBox.shrink();

        return AnimatedSize(
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
          child: !_isExpanded
              ? const SizedBox.shrink()
              : Column(
                  children: [
                    const SizedBox(height: 12),
                    _buildInfoRow(
                      context,
                      left: StockInfoItem("market_cap".tr(), stock.marketValue ?? 0.0, Colors.grey,
                          isVolume: true, isBold: true),
                      right: StockInfoItem("turnover_rate".tr(), stock.turnover ?? 0.0, Colors.grey, isPercent: true),
                    ),
                    const SizedBox(height: 12),
                    _buildInfoRow(
                      context,
                      left: StockInfoItem("total_shares".tr(), stock.totalShares ?? 0, Colors.grey, isVolume: true),
                      right: StockInfoItem("pe_ttm".tr(), stock.peTtm ?? 0.0, Colors.grey, isPercent: true),
                    ),
                    const SizedBox(height: 12),
                    _buildInfoRow(
                      context,
                      left: StockInfoItem("high_52w".tr(), stock.high52w ?? 0.0, Colors.grey, isPrice: true),
                      right: StockInfoItem("low_52w".tr(), stock.low52w ?? 0.0, Colors.grey, isPrice: true),
                    ),
                    const SizedBox(height: 12),
                    _buildInfoRow(
                      context,
                      left: StockInfoItem("amplitude".tr(), stock.amplitude ?? 0.0, Colors.grey, isPercent: true),
                      right: StockInfoItem("pe_static".tr(), stock.peStatic ?? 0.0, Colors.grey, isPercent: true),
                    ),
                    const SizedBox(height: 12),
                    _buildInfoRow(
                      context,
                      left: StockInfoItem("dividend".tr(), stock.dividend ?? 0.0, Colors.grey, isPrice: true),
                      right: StockInfoItem("pe_dynamic".tr(), stock.peLyr ?? 0.0, Colors.grey, isPercent: true),
                    ),
                    const SizedBox(height: 12),
                    _buildInfoRow(
                      context,
                      left:
                          StockInfoItem("dividend_rate".tr(), stock.dividendRate ?? 0.0, Colors.grey, isPercent: true),
                      right: StockInfoItem("pb_ratio".tr(), stock.pb ?? 0.0, Colors.grey, isPercent: true),
                    ),
                    const SizedBox(height: 12),
                    _buildInfoRow(
                      context,
                      left: StockInfoItem("lot_size".tr(), stock.lotSize ?? 0, Colors.grey,
                          isBold: true, isLotSize: true),
                      right: const SizedBox.shrink(),
                    ),
                  ],
                ),
        );
      },
    );
  }

  /// Builds a row with two stock info items.
  Widget _buildInfoRow(BuildContext context, {required Widget left, required Widget right}) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Expanded(child: left),
        const SizedBox(width: 12),
        Expanded(child: right),
      ],
    );
  }

  /// Builds the expand/collapse button.
  Widget _buildExpandButton() {
    return GestureDetector(
      onTap: () => setState(() => _isExpanded = !_isExpanded),
      child: Container(
        color: Colors.transparent,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              _isExpanded ? Icons.expand_less : Icons.expand_more,
              color: Colors.grey,
            ),
            Text(
              _isExpanded ? "click_to_collapse".tr() : "click_to_expand".tr(),
              style: const TextStyle(color: Colors.grey, fontSize: 12),
            ),
          ],
        ),
      ),
    );
  }
}

/// Formats the timestamp based on market status and stock data.
String _formatTimestamp({MarketStatusResponse? response, StockInfoData? stock}) {
  if (response == null) return "--";
  final status = response.data?.status ?? "";
  int? time;

  switch (status) {
    case 0:
    case 1:
    case 6:
      time = response.data?.openTime;
      break;
    case 2:
      time = stock?.latestTime;
      break;
    case 3:
    case 4:
      time = response.data?.closeTime;
      break;
  }

  if (time == null) return "--";
  return TimeZoneHelper.formatTimeInZone(
    time,
    countryTimeZones[stock?.market ?? 'CN'] ?? 'Asia/Shanghai',
    format: TimeFormat.full,
  );
}
