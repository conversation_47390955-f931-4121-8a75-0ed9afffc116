import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_bounceable/flutter_bounceable.dart';
import 'package:gp_stock_app/core/theme/custom_text_theme.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/account/domain/constants/kline_constants.dart';
import 'package:gp_stock_app/features/account/domain/models/kline_option.dart';
import 'package:gp_stock_app/features/account/logic/trading/trading_cubit.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';
import 'package:gp_stock_app/shared/models/dropdown/dropdown_value.dart';
import 'package:gp_stock_app/shared/theme/font_pallette.dart';
import 'package:gp_stock_app/shared/theme/my_color_scheme.dart';
import 'package:gp_stock_app/shared/widgets/dropdown/common_dropdown.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';

class KlineSelector extends StatelessWidget {
  const KlineSelector({
    super.key,
    required this.instrument,
  });

  final String instrument;

  @override
  Widget build(BuildContext context) {
    return BlocSelector<TradingCubit, TradingState, (DataStatus, KlineOption?)>(
      selector: (state) => (state.klineDetailListStatus, state.klineOption),
      builder: (context, state) {
        final selectedOption = state.$2;
        final isLoading = state.$1 == DataStatus.loading;

        return AbsorbPointer(
          absorbing: isLoading,
          child: SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            padding: EdgeInsets.fromLTRB(20, 0, 0, 0),
            child: Row(
              spacing: 10,
              children: [
                Row(
                  spacing: 20,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: KlineConstants.options.map((option) {
                    bool isSelected = selectedOption?.id == option.id;
                    return Container(
                      color: context.theme.cardColor,
                      child: Bounceable(
                        onTap: () {
                          context.read<TradingCubit>().getKlineDetailList(instrument, option);
                        },
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.start,
                          spacing: 4,
                          children: [
                            Text(
                              option.label.tr(),
                              style: FontPalette.bold12.copyWith(
                                  color: isLoading
                                      ? context.colorTheme.textRegular.withValues(alpha: 0.3)
                                      : isSelected
                                          ? context.theme.primaryColor
                                          : null),
                            ),
                            Opacity(
                              opacity: isSelected ? 1 : 0,
                              child: Container(
                                width: 30,
                                height: 2,
                                color: isLoading
                                    ? context.colorTheme.textRegular.withValues(alpha: 0.3)
                                    : context.theme.primaryColor,
                              ),
                            ),
                          ],
                        ),
                      ),
                    );
                  }).toList(),
                ),
                Builder(
                  builder: (context) {
                    final exists = KlineConstants.subOptions.any((e) => e.id == selectedOption?.id);
                    final option = exists ? selectedOption! : KlineConstants.subOptions[0];
                    return Container(
                      color: Colors.transparent,
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.start,
                        spacing: 4,
                        children: [
                          SizedBox(
                            width: 140,
                            child: CommonDropdown(
                              isSuperDense: true,
                              height: 30,
                              showSearchBox: false,
                              isEnabled: true,
                              selectedItem: DropDownValue(
                                value: option.label.tr(),
                                id: option.id,
                              ),
                              dropDownValue: KlineConstants.subOptions
                                  .map(
                                    (e) => DropDownValue(
                                      value: e.label.tr(),
                                      id: e.id,
                                    ),
                                  )
                                  .toList(),
                              onChanged: (value) {
                                final selectedOption = KlineConstants.subOptions.firstWhere(
                                    (e) => e.label == value.value,
                                    orElse: () => KlineConstants.subOptions[0]);
                                context.read<TradingCubit>().getKlineDetailList(instrument, selectedOption);
                              },
                              hintText: '',
                              fillColor: Colors.transparent,
                              borderRadius: 5.gr,
                              textStyle: FontPalette.bold12.copyWith(
                                  color: isLoading
                                      ? context.colorTheme.textRegular.withValues(alpha: 0.3)
                                      : exists
                                          ? context.theme.primaryColor
                                          : null),
                            ),
                          ),
                          Opacity(
                            opacity: exists ? 1 : 0,
                            child: Container(
                              width: 30,
                              height: 2,
                              color: isLoading
                                  ? context.colorTheme.textRegular.withValues(alpha: 0.3)
                                  : context.theme.primaryColor,
                            ),
                          ),
                        ],
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
