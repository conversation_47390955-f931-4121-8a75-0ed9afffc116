import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/core/theme/custom_text_theme.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/account/domain/models/quotes/company_info/company_info_response.dart';
import 'package:gp_stock_app/features/account/logic/trading/trading_cubit.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';
import 'package:gp_stock_app/shared/mixin/animation.dart';
import 'package:gp_stock_app/shared/theme/font_pallette.dart';
import 'package:gp_stock_app/shared/widgets/shimmer/shimmer_widget.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../../../../../shared/theme/my_color_scheme.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';

class QuotesCompanyInfo extends StatelessWidget with StaggeredAnimation {
  const QuotesCompanyInfo({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocSelector<TradingCubit, TradingState, (DataStatus, CompanyInfoResponse?)>(
      selector: (state) => (state.companyInfoFetchStatus, state.companyInfo),
      builder: (context, state) {
        if (state.$1 == DataStatus.loading) {
          return ShimmerWidget(height: 300.gh);
        }

        if (state.$1 == DataStatus.failed || state.$2?.data == null) {
          return Center(child: Icon(Icons.error));
        }

        final data = state.$2!.data!;

        return SingleChildScrollView(
          child: Container(
            color: context.theme.cardColor,
            padding: const EdgeInsets.fromLTRB(20, 16, 20, 16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: staggeredAnimationSlide(
                children: [
                  _buildHeader('basicOverview'.tr(), context),
                  const SizedBox(height: 10),
                  _buildInfoRow('symbol'.tr(), data.symbol, context: context),
                  _buildInfoRow('chairman'.tr(), data.chairman, context: context),
                  _buildInfoRow('registeredCapital'.tr(), data.registerCapital, context: context),
                  _buildInfoRow('companyName'.tr(), data.companyName, bold: true, context: context),
                  _buildInfoRow('companyIntroduction'.tr(), data.companyProfile, context: context),
                  _buildInfoRow('companyLegalPerson'.tr(), data.legalPerson, context: context),
                  _buildInfoRow('secretary'.tr(), data.chairmanSecretary, context: context),
                  _buildInfoRow('companyWebsite'.tr(), data.companyWebsite, isLink: true, context: context),
                  _buildInfoRow('industry'.tr(), data.industry, bold: true, context: context),
                  _buildInfoRow('mainBusiness'.tr(), data.mainBusiness, context: context),
                  _buildInfoRow('establishmentDate'.tr(), data.establishDate, context: context),
                  _buildInfoRow('registeredAddress'.tr(), data.registeredAddress, context: context),
                  _buildInfoRow('companyStaff'.tr(), data.staffNumber, context: context),
                  _buildInfoRow('officeAddress'.tr(), data.officeAddress, context: context),
                  _buildInfoRow('contactNumber'.tr(), data.contactTelephone, isPhone: true, context: context),
                  _buildInfoRow('emailId'.tr(), data.email, isEmail: true, context: context),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildHeader(String title, BuildContext context) {
    return Text(
      title,
      style: TextStyle(
        fontSize: 18,
        fontWeight: FontWeight.bold,
        color: context.colorTheme.textPrimary,
      ),
    );
  }

  Widget _buildInfoRow(
    String title,
    String? value, {
    bool bold = false,
    bool isLink = false,
    bool isPhone = false,
    bool isEmail = false,
    required BuildContext context,
  }) {
    // Display '-' if value is null or empty
    final displayValue = (value == null || value.isEmpty) ? '-' : value;

    Future<void> launchURL(String url) async {
      final Uri uri = Uri.parse(url);
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri, mode: LaunchMode.externalApplication);
      }
    }

    String getUrl() {
      if (isPhone) return 'tel:$value';
      if (isEmail) return 'mailto:$value';
      return value ?? '';
    }

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              title,
              style: SecFontPalette.normal14.copyWith(color: context.colorTheme.textPrimary),
            ),
          ),
          Expanded(
            child: GestureDetector(
              onTap: (isLink || isPhone || isEmail) && value != null && value.isNotEmpty
                  ? () => launchURL(getUrl())
                  : null, // Disable tap if no value
              child: Text(
                displayValue,
                style: SecFontPalette.normal14.copyWith(
                  color: (isLink || isPhone || isEmail) && value != null && value.isNotEmpty
                      ? Colors.blueAccent
                      : context.colorTheme.textRegular,
                  fontWeight: bold ? FontWeight.bold : FontWeight.normal,
                ),
                softWrap: true,
                textAlign: TextAlign.end,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
