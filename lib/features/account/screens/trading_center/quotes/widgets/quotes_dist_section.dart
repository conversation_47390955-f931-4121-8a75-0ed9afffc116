import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/core/theme/custom_text_theme.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/account/domain/models/quotes/dist_flow/dist_flow_response.dart';
import 'package:gp_stock_app/features/account/logic/trading/trading_cubit.dart';
import 'package:gp_stock_app/features/account/screens/trading_center/quotes/widgets/dist_bar_chart.dart';
import 'package:gp_stock_app/features/account/screens/trading_center/quotes/widgets/dist_flow_chart.dart';
import 'package:gp_stock_app/features/account/screens/trading_center/quotes/widgets/dist_pie_chart.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';
import 'package:gp_stock_app/shared/mixin/animation.dart';
import 'package:gp_stock_app/shared/theme/font_pallette.dart';
import 'package:gp_stock_app/shared/widgets/shimmer/shimmer_widget.dart';

import '../../../../../../shared/theme/my_color_scheme.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';

class QuotesDistSection extends StatelessWidget with StaggeredAnimation {
  const QuotesDistSection({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocSelector<TradingCubit, TradingState, (DataStatus, DistFlowResponse?, DateTime?)>(
        selector: (state) => (state.distFlowFetchStatus, state.distFlowResponse, state.updatedTime),
        builder: (context, state) {
          if (state.$1 == DataStatus.loading) {
            return ShimmerWidget(height: 300.gh);
          }

          if (state.$1 == DataStatus.failed || state.$2?.data == null) {
            return Center(child: Icon(Icons.error));
          }

          final data = state.$2!.data!;
          final updatedTime = state.$3;

          return SingleChildScrollView(
            child: Column(
              spacing: 12,
              children: [
                Container(
                  color: context.theme.cardColor,
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    children: staggeredAnimationSlide(
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Expanded(
                              child: Text(
                                'distFlow'.tr(),
                                style: SecFontPalette.bold14.copyWith(
                                  color: context.colorTheme.textPrimary,
                                ),
                              ),
                            ),
                            if (updatedTime != null)
                              Expanded(
                                flex: 2,
                                child: Text(
                                  "${'updatedAt'.tr()}： ${DateFormat('yyyy-MM-dd HH:mm').format(updatedTime)}",
                                  style: TextStyle(fontSize: 12, color: Colors.grey),
                                ),
                              ),
                          ],
                        ),
                        10.verticalSpace,
                        DistPieChart(data: data),
                        30.verticalSpace,
                        DistBarChart(data: data),
                        30.verticalSpace,
                      ],
                    ),
                  ),
                ),
                Container(
                  color: context.theme.cardColor,
                  padding: const EdgeInsets.all(16.0),
                  child: DistFlowChart(data: data),
                ),
                30.verticalSpace,
              ],
            ),
          );
        });
  }
}
