import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/core/theme/custom_text_theme.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/account/domain/models/account_summary/contract_summary_response.dart';
import 'package:gp_stock_app/features/account/logic/account/account_cubit.dart';
import 'package:gp_stock_app/features/contract/widgets/amount_row.dart';
import 'package:gp_stock_app/shared/app/utilities/easy_loading.dart';
import 'package:gp_stock_app/shared/routes/navigator.dart';
import 'package:gp_stock_app/shared/widgets/buttons/custom_material_button.dart';
import 'package:gp_stock_app/shared/widgets/shadow_box.dart';

import '../../../../core/utils/convert_helper.dart';
import '../../../../core/utils/functions.dart';
import '../../../../shared/constants/enums.dart';
import '../../../../shared/theme/color_pallette.dart';
import '../../../../shared/theme/my_color_scheme.dart';
import '../../logic/contract_terminate/contract_terminate_cubit.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';

class TerminateContractScreen extends StatelessWidget {
  final ContractSummaryData contractSummary;
  const TerminateContractScreen({super.key, required this.contractSummary});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: context.theme.cardColor,
        title: Text('terminateContract'.tr()),
        centerTitle: true,
      ),
      body: Padding(
        padding: EdgeInsets.all(16.gr),
        child: Column(
          children: [
            Expanded(
              child: SingleChildScrollView(
                child: Column(
                  children: [
                    _buildContractInfoSection(context: context, contractSummary: contractSummary),
                    16.verticalSpace,
                    _buildAmountSection(context: context, contractSummary: contractSummary),
                  ],
                ),
              ),
            ),
            24.verticalSpace,
            _buildTerminateButton(context: context, contractSummary: contractSummary),
          ],
        ),
      ),
    );
  }

  Widget _buildContractInfoSection({required BuildContext context, required ContractSummaryData contractSummary}) {
    String formatDateRange() {
      try {
        return '${ConvertHelper.formatDateGeneral(contractSummary.openTime ?? '')} - ${ConvertHelper.formatDateGeneral(contractSummary.expireTime ?? '')}';
      } catch (e) {
        return ConvertHelper.formatDateGeneral(contractSummary.openTime ?? '');
      }
    }

    return ShadowBox(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          AmountRow(
            title: 'contractType'.tr(),
            value: getContractName(
              marketType: contractSummary.marketType,
              type: contractSummary.type,
              periodType: contractSummary.periodType,
              multiple: contractSummary.multiple,
              id: contractSummary.id,
            ),
          ),
          12.verticalSpace,
          AmountRow(
            title: 'period'.tr(),
            value: getPeriodType(contractSummary.periodType),
          ),
          12.verticalSpace,
          AmountRow(
            title: 'date'.tr(),
            value: formatDateRange(),
          ),
          12.verticalSpace,
          AmountRow(
            title: 'contractMultiple'.tr(),
            value: '${contractSummary.multiple}${'times'.tr()}',
          ),
        ],
      ),
    );
  }

// Helper function to get period type text
  String getPeriodType(int? period) => switch (period) {
        1 => 'daily'.tr(),
        2 => 'weekly'.tr(),
        3 => 'monthly'.tr(),
        _ => 'daily'.tr(),
      };

  Widget _buildAmountSection({required BuildContext context, required ContractSummaryData contractSummary}) {
    return ShadowBox(
      child: Column(
        children: [
          AmountRow(
            title: 'totalMargin'.tr(),
            amount: contractSummary.totalPower,
          ),
          12.verticalSpace,
          AmountRow(
            title: 'initialMargin'.tr(),
            amount: contractSummary.initCash,
          ),
          12.verticalSpace,
          AmountRow(
            title: 'warningLine'.tr(),
            amount: contractSummary.warnRemindAmount,
          ),
          12.verticalSpace,
          AmountRow(
            title: 'stopLossLine'.tr(),
            amount: contractSummary.closeRemindAmount,
          ),
          12.verticalSpace,
          AmountRow(
            title: 'interestAmount'.tr(),
            amount: contractSummary.interestAmount,
          ),
          12.verticalSpace,
          AmountRow(
            title: 'marketValue'.tr(),
            amount: contractSummary.positionAmount ?? 0.00,
          ),
          12.verticalSpace,
          AmountRow(
            title: 'expandedMargin'.tr(),
            amount: contractSummary.expendAmount,
          ),
          12.verticalSpace,
          AmountRow(
            title: 'supplementLoss'.tr(),
            amount: contractSummary.coverLossAmount,
          ),
          12.verticalSpace,
          AmountRow(
            title: 'floatingProfitLoss'.tr(),
            amount: contractSummary.winAmount,
          ),
          12.verticalSpace,
          AmountRow(
            title: 'withdrawAmount'.tr(),
            amount: contractSummary.withdrawAmount,
          ),
          12.verticalSpace,
          AmountRow(
            title: 'frozenAmount'.tr(),
            amount: contractSummary.freezePower,
          ),
        ],
      ),
    );
  }

  Widget _buildTerminateButton({required BuildContext context, required ContractSummaryData contractSummary}) {
    return BlocConsumer<ContractTerminateCubit, ContractTerminateState>(
      listenWhen: (previous, current) => previous.terminateStatus != current.terminateStatus,
      listener: (context, state) {
        if (state.terminateStatus == DataStatus.success) {
          context.read<AccountCubit>().getContractSummary();
          Navigator.popUntil(navigatorKey.currentContext!, (route) => route.isFirst);
        }
        if (state.terminateStatus == DataStatus.failed) {
          GPEasyLoading.showToast(state.errorMessage ?? 'somethingWentWrong'.tr());
        }
      },
      builder: (context, state) {
        return SizedBox(
          width: double.infinity,
          child: CustomMaterialButton(
            onPressed: () =>
                context.read<ContractTerminateCubit>().terminateContract(contractId: contractSummary.id.toString()),
            buttonText: 'terminate'.tr(),
            color: ColorPalette.redColor,
            borderColor: ColorPalette.redColor,
            borderRadius: 8.gr,
            textColor: Colors.white,
            isLoading: state.terminateStatus == DataStatus.loading,
          ),
        );
      },
    );
  }
}
