import 'package:auto_size_text/auto_size_text.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:gp_stock_app/core/theme/custom_text_theme.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/contract/domain/models/contract_calculation/contract_calculation_model.dart';
import 'package:gp_stock_app/features/contract/domain/models/contract_model.dart';
import 'package:gp_stock_app/features/contract/widgets/app_info_bottom_sheet.dart';
import 'package:gp_stock_app/shared/app/extension/color_extension.dart';
import 'package:gp_stock_app/shared/app/extension/helper.dart';
import 'package:gp_stock_app/shared/app/extension/string_extension.dart';
import 'package:gp_stock_app/shared/theme/font_pallette.dart';
import 'package:gp_stock_app/shared/theme/my_color_scheme.dart';
import 'package:gp_stock_app/shared/widgets/buttons/custom_material_button.dart';
import 'package:gp_stock_app/shared/widgets/buttons/custom_radio_button.dart';
import 'package:gp_stock_app/shared/widgets/dropdown/input_dropdown.dart';

import '../../../shared/constants/enums.dart';
import '../../../shared/logic/exchange_rate/exchange_rate_cubit.dart';
import '../../../shared/models/dropdown/dropdown_value.dart';
import '../../../shared/widgets/shimmer/shimmer_widget.dart';
import '../domain/models/contract_activity/contract_activity_response.dart';
import '../logic/contract_activity/contract_activity_cubit.dart';
import '../widgets/activity_summary_dialog.dart';
import '../widgets/amount_row.dart';
import '../widgets/future_funding_widget.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';

/// Page for applying for experience contract  and bonus contract.
class ContractActivityScreen extends StatefulWidget {
  final ContractType contractType;
  final MainContractType mainContractType;
  const ContractActivityScreen({
    super.key,
    required this.contractType,
    required this.mainContractType,
  });

  @override
  State<ContractActivityScreen> createState() => _ContractActivityScreenState();
}

class _ContractActivityScreenState extends State<ContractActivityScreen> {
  final amountController = TextEditingController();

  @override
  void initState() {
    super.initState();
    final rates = context.read<ExchangeRateCubit>().getCurrencyRateConfig('CNY');
    Helper.afterInit(
      () => context.read<ContractActivityCubit>()
        ..getContractActivity(
            type: widget.contractType.value, exchangeRate: rates.rate, parentType: widget.mainContractType.value)
        ..setContractType(widget.mainContractType, widget.contractType),
    );
  }

  @override
  void dispose() {
    amountController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {

    return BlocListener<ContractActivityCubit, ContractActivityState>(
      listenWhen: (previous, current) => current.selectedAmount != previous.selectedAmount,
      listener: (context, state) {
        amountController.text = state.selectedAmount.toString();
      },
      child: Scaffold(
        backgroundColor: Theme.of(context).scaffoldBackgroundColor,
        appBar: AppBar(
          backgroundColor: Colors.transparent,
          surfaceTintColor: Colors.transparent,
          elevation: 0,
          iconTheme: IconThemeData(
            color: context.colorTheme.textPrimary,
          ),
          title: Text(
            '${'applyFor'.tr()} [${widget.contractType.title(widget.mainContractType).tr()}]',
            style: FontPalette.medium16.copyWith(
              color: context.colorTheme.textPrimary,
            ),
          ),
        ),
        body: BlocConsumer<ContractActivityCubit, ContractActivityState>(
          listenWhen: (previous, current) =>
              current.error.isNotNullNorEmpty && current.contractActivityFetchStatus.isFailed,
          listener: (context, state) {
            EasyLoading.showToast(state.error);
          },
          builder: (context, state) {
            return SingleChildScrollView(
              child: Padding(
                padding: EdgeInsets.symmetric(horizontal: 16.gw),
                child: Column(
                  children: [
                    _buildContractDetailsCard(context, state, widget.mainContractType),
                    12.verticalSpace,
                    _CardContainer(
                      child: _ContractDetailsSection(
                        contractType: widget.contractType,
                        amountController: amountController,
                      ),
                    ),
                    30.verticalSpace,
                    _SubmitSection(
                      state: state,
                      contractType: widget.contractType,
                      amountController: amountController,
                      mainContractType: widget.mainContractType,
                    ),
                  ],
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildContractDetailsCard(
      BuildContext context, ContractActivityState state, MainContractType mainContractType) {
    return _CardContainer(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _ContractTypeSection(
              contractActivity: state.contractActivity,
              mainContractType: widget.mainContractType,
              contractType: widget.contractType),
          const _Divider(),
          _PeriodSection(contractType: widget.contractType),
          const _Divider(),
          _LeverageSection(contractType: widget.contractType),
          const _Divider(),
          _AmountSection(contractType: widget.contractType),
        ],
      ),
    );
  }
}

class _CardContainer extends StatelessWidget {
  final Widget child;

  const _CardContainer({required this.child});

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: context.theme.cardColor,
        borderRadius: BorderRadius.circular(8.gr),
      ),
      child: Padding(
        padding: EdgeInsets.symmetric(
          horizontal: 14.gw,
          vertical: 12.gh,
        ),
        child: child,
      ),
    );
  }
}

class _ContractTypeSection extends StatelessWidget {
  final ContractActivityData? contractActivity;
  final ContractType contractType;
  final MainContractType mainContractType;

  const _ContractTypeSection(
      {required this.contractActivity, required this.mainContractType, required this.contractType});

  @override
  Widget build(BuildContext context) {
    final hasActivityRiskMap = contractActivity?.activityRiskMap?.isNotEmpty == true;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'contractType'.tr(),
              style: FontPalette.medium16.copyWith(
                color: context.colorTheme.textPrimary,
              ),
            ),
            12.horizontalSpace,
            GestureDetector(
              onTap: () => _showContractInstructionDialog(context, contractType: contractType),
              child: Icon(
                Icons.help_outline,
                color: context.colorTheme.textPrimary,
              ),
            ),
          ],
        ),
        12.verticalSpace,
        SizedBox(
          height: 30.gh,
          child: mainContractType == MainContractType.futures
              ? FutureFundingWidget()
              : hasActivityRiskMap
                  ? _buildContractTypeList(context)
                  : _buildShimmerLoader(),
        ),
      ],
    );
  }

  Widget _buildContractTypeList(BuildContext context) {
    return ListView.separated(
      scrollDirection: Axis.horizontal,
      shrinkWrap: true,
      itemCount: contractActivity?.activityRiskMap?.length ?? 0,
      separatorBuilder: (_, __) => 12.horizontalSpace,
      itemBuilder: (context, index) {
        final item = contractActivity?.activityRiskMap?[index];
        if (item == null) return const SizedBox.shrink();

        return BlocSelector<ContractActivityCubit, ContractActivityState, (ActivityRiskMap, String)>(
          selector: (state) => (
            state.selectedMarket ?? ActivityRiskMap(),
            state.currency ?? '',
          ),
          builder: (context, selectedMarket) {
            final marketType = item.marketType ?? '';
            final title = contractMarketTranslation[marketType]?.tr() ?? '';
            return _SelectableButton(
              title: title,
              isSelected: selectedMarket.$1.activityId == item.activityId,
              onTap: () {
                final newMarketCurrency = switch (item.marketType) {
                  'CN' => 'CNY',
                  'HK' => 'HKD',
                  'US' => 'USD',
                  _ => selectedMarket.$2,
                };
                final newRate = context.read<ExchangeRateCubit>().getCurrencyRateConfig(newMarketCurrency);
                context.read<ContractActivityCubit>().selectMarket(item, newRate.rate);
              },
            );
          },
        );
      },
    );
  }

  Widget _buildShimmerLoader() {
    return ShimmerWidget(
      width: double.infinity,
      height: 30.gh,
      child: Container(
        decoration: BoxDecoration(
          color: Colors.grey[300],
          borderRadius: BorderRadius.circular(8),
        ),
      ),
    );
  }

  void _showContractInstructionDialog(BuildContext context, {required ContractType contractType}) {
    final selectedMarket = context.read<ContractActivityCubit>().state.selectedMarket;
    final activityRule = selectedMarket?.activityRule;
    final hasInstructions = activityRule != null && activityRule.isNotEmpty;
    final contractTypeName = contractType.translationKey.tr();

    showDialog(
      context: context,
      builder: (context) {

        return Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16.gr),
          ),
          insetPadding: EdgeInsets.symmetric(horizontal: 24.gw),
          child: Container(
            width: 0.85.gsw,
            height: 0.6.gsh,
            padding: EdgeInsets.all(16.gw),
            decoration: BoxDecoration(
              color: context.theme.cardColor,
              borderRadius: BorderRadius.circular(16.gr),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      '$contractTypeName ${'instructions'.tr()}',
                      style: FontPalette.bold16.copyWith(
                        color: context.colorTheme.textPrimary,
                      ),
                    ),
                    IconButton(
                      icon: Icon(
                        Icons.close,
                        color: context.colorTheme.textPrimary,
                        size: 20.gw,
                      ),
                      onPressed: () => Navigator.pop(context),
                      padding: EdgeInsets.zero,
                      constraints: BoxConstraints(),
                    ),
                  ],
                ),
                Divider(color: context.theme.dividerColor),
                8.verticalSpace,
                Expanded(
                  child: SingleChildScrollView(
                    physics: BouncingScrollPhysics(),
                    child: hasInstructions
                        ? Text(
                            activityRule,
                            style: FontPalette.normal14.copyWith(
                              color: context.colorTheme.textPrimary,
                              height: 1.5,
                            ),
                          )
                        : Center(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(
                                  Icons.info_outline,
                                  size: 48.gw,
                                  color: context.colorTheme.textRegular.withAlpha(128),
                                ),
                                8.verticalSpace,
                                Text(
                                  'no_results_found'.tr(),
                                  style: FontPalette.medium14.copyWith(
                                    color: context.colorTheme.textRegular,
                                  ),
                                ),
                              ],
                            ),
                          ),
                  ),
                ),
                8.verticalSpace,
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: () => Navigator.pop(context),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: context.theme.primaryColor,
                      foregroundColor: Colors.white,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(20.gr),
                      ),
                      padding: EdgeInsets.symmetric(vertical: 12.gh),
                    ),
                    child: Text(
                      'close'.tr(),
                      style: FontPalette.medium14.copyWith(
                        color: Colors.white,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}

class _AmountSection extends StatelessWidget {
  final ContractType contractType;

  const _AmountSection({required this.contractType});

  @override
  Widget build(BuildContext context) {
    return BlocSelector<ContractActivityCubit, ContractActivityState,
        (int?, List<double>, ContractModelAmount, ActivityRiskMap, String, ContractActivityData?)>(
      selector: (state) => (
        state.selectedAmount,
        state.contractActivity?.activityRiskMap?.first.applyAmountList ?? [],
        state.contractModelAmount ?? ContractModelAmount(),
        state.selectedMarket ?? ActivityRiskMap(),
        state.currency ?? '',
        state.contractActivity,
      ),
      builder: (context, data) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            AmountRow(
              title: 'giveDay'.tr(),
              value: '${data.$4.giveDay ?? 0} ${'day'.tr()}',
            ),
            if (contractType == ContractType.bonus) ...[
              12.verticalSpace,
              AmountRow(
                title: 'bonusAmount'.tr(),
                amount: data.$4.giveAmount != null && data.$4.giveAmount != 0
                    ? data.$4.giveAmount
                    : ((data.$4.giveRatio ?? 0) * (data.$1 ?? 0)) / 100,
                currency: data.$5,
              ),
              12.verticalSpace,
              AmountRow(
                title: 'availableBalance'.tr(),
                amount: data.$6?.useAmount ?? 0,
                currency: data.$6?.currency ?? '',
              ),
              12.verticalSpace,
              AmountRow(
                title: 'interestCoupon'.tr(),
                amount: data.$6?.interestCash ?? 0,
                currency: data.$6?.currency ?? '',
              ),
            ],
          ],
        );
      },
    );
  }
}

class _PeriodSection extends StatelessWidget {
  final ContractType contractType;

  const _PeriodSection({required this.contractType});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'period'.tr(),
          style: FontPalette.medium16.copyWith(
            color: context.colorTheme.textPrimary,
          ),
        ),
        12.verticalSpace,
        SizedBox(
          height: 30.gh,
          child: BlocSelector<ContractActivityCubit, ContractActivityState, ActivityRiskMap?>(
            selector: (state) => state.selectedMarket,
            builder: (context, selectedMarket) {
              if (selectedMarket == null) return _buildShimmerLoader();
              return _SelectableButton(
                title: 'contract.period_1'.tr(),
                isSelected: true,
                onTap: () {},
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildShimmerLoader() {
    return ShimmerWidget(
      width: double.infinity,
      height: 30.gh,
      child: Container(
        decoration: BoxDecoration(
          color: Colors.grey[300],
          borderRadius: BorderRadius.circular(8),
        ),
      ),
    );
  }
}

class _LeverageSection extends StatelessWidget {
  final ContractType contractType;

  const _LeverageSection({required this.contractType});

  @override
  Widget build(BuildContext context) {

    return Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
      Text(
        'leverage'.tr(),
        style: FontPalette.medium14.copyWith(
          color: context.colorTheme.textPrimary,
        ),
      ),
      12.verticalSpace,
      SizedBox(
        height: 30.gh,
        child: BlocSelector<ContractActivityCubit, ContractActivityState, ActivityRiskMap?>(
          selector: (state) => state.selectedMarket,
          builder: (context, selectedMarket) {
            final multiple = selectedMarket?.multiple;
            if (multiple == null) return _buildShimmerLoader();

            return _SelectableButton(
              title: '$multiple${'xTimes'.tr()}',
              isSelected: true,
              onTap: () {},
            );
          },
        ),
      ),
    ]);
  }

  Widget _buildShimmerLoader() {
    return ShimmerWidget(
      width: double.infinity,
      height: 30.gh,
      child: Container(
        decoration: BoxDecoration(
          color: Colors.grey[300],
          borderRadius: BorderRadius.circular(8),
        ),
      ),
    );
  }
}

class _ContractDetailsSection extends StatelessWidget {
  final ContractType contractType;
  final TextEditingController amountController;
  const _ContractDetailsSection({required this.contractType, required this.amountController});

  @override
  Widget build(BuildContext context) {

    return BlocSelector<
        ContractActivityCubit,
        ContractActivityState,
        (
          int?,
          List<DropDownValue>,
          ContractModelAmount,
          ActivityRiskMap,
          String,
          ContractActivityData?,
          ContractCalculationModel?
        )>(
      selector: (state) => (
        state.selectedAmount,
        [],
        state.contractModelAmount ?? ContractModelAmount(),
        state.selectedMarket ?? ActivityRiskMap(),
        state.currency ?? '',
        state.contractActivity,
        state.bonusContractCalculation,
      ),
      builder: (context, data) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (contractType == ContractType.bonus) ...[
              Text(
                'contractMargin'.tr(),
                style: FontPalette.medium14.copyWith(
                  color: context.colorTheme.textPrimary,
                ),
              ),
              12.verticalSpace,
            ],
            if (contractType == ContractType.experience) ...[
              Text(
                'giftAmount'.tr(),
                style: FontPalette.medium14.copyWith(
                  color: context.colorTheme.textPrimary,
                ),
              ),
              12.verticalSpace,
            ],
            contractType == ContractType.experience
                ? Container(
                    height: 48.gh,
                    decoration: BoxDecoration(
                      color: context.theme.scaffoldBackgroundColor,
                      borderRadius: BorderRadius.circular(8.gr),
                      border: Border.all(color: context.theme.dividerColor),
                    ),
                    padding: EdgeInsets.symmetric(horizontal: 16.gw),
                    alignment: Alignment.centerLeft,
                    child: Text(
                      // Use giveAmount for experience contracts
                      '${data.$4.giveAmount != null ? data.$4.giveAmount!.toStringAsFixed(2) : '0.00'} ${data.$5}',
                      style: FontPalette.normal14.copyWith(
                        color: context.colorTheme.textPrimary,
                        fontFamily: 'Akzidenz-Grotesk',
                      ),
                    ),
                  )
                : InputDropdownWidget<double>(
                    items: data.$4.applyAmountList?.map((e) => e).toList() ?? [],
                    textController: amountController,
                    itemBuilder: (dynamic item) => Center(child: Text(item.toStringAsFixed(0))),
                    onTextChanged: (value) {
                      if (value.isNotEmpty && int.tryParse(value) != null) {
                        final rates = context.read<ExchangeRateCubit>().getCurrencyRateConfig(data.$5);
                        context.read<ContractActivityCubit>().selectAmount(int.parse(value), rates.rate);
                      }
                    },
                    onDropdownChanged: (value) {
                      if (value != null) {
                        final rates = context.read<ExchangeRateCubit>().getCurrencyRateConfig(data.$5);
                        context.read<ContractActivityCubit>().selectAmount(value.toInt(), rates.rate);
                      }
                    },
                  ),
            16.verticalSpace,
            _buildContractDetails(context, data, contractType),
          ],
        );
      },
    );
  }

  Widget _buildContractDetails(
      BuildContext context,
      (
        int?,
        List<DropDownValue>,
        ContractModelAmount,
        ActivityRiskMap,
        String,
        ContractActivityData?,
        ContractCalculationModel?
      ) data,
      ContractType contractType) {
    final amount = data.$3;
    final currency = data.$5;
    final contractCalculation = data.$7;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        AmountRow(
          title: 'totalMargin'.tr(),
          amount: amount.totalTadingFunds ?? 0, // Note: There's a typo in the API field name
          currency: currency,
        ),
        12.verticalSpace,
        AmountRow(
          title: 'lossWarningLine'.tr(),
          amount: amount.lossWarningLine ?? 0,
          currency: currency,
        ),
        12.verticalSpace,
        AmountRow(
          title: 'liquidationLine'.tr(),
          amount: amount.lossFlatLine ?? 0,
          currency: currency,
        ),
        if (contractType == ContractType.bonus) ...[
          12.verticalSpace,
          AmountRow(
            title: 'capitalInterestRate'.tr(),
            amount: contractCalculation?.rateAmount ?? 0,
            currency: '$currency/${'day'.tr()}',
          ),
          12.verticalSpace,
          AmountRow(
            title: 'interestReduceAmount'.tr(),
            amount: contractCalculation?.deductInterestCashCNY ?? 0,
            isCurrency: true,
            currency: currency,
          ),
          12.verticalSpace,
          AmountRow(
            title: 'actualPaymentAmount2'.tr(),
            amount: contractCalculation?.deductCanUseCashCNY ?? 0,
            isCurrency: true,
            prefix: currency != 'CNY' ? '≈ ' : '',
            currency: 'CNY',
          ),
        ]
      ],
    );
  }
}

/// Section for agreement checkbox and submit button
class _SubmitSection extends StatelessWidget {
  final ContractActivityState state;
  final ContractType contractType;
  final MainContractType mainContractType;
  final TextEditingController amountController;
  const _SubmitSection({
    required this.state,
    required this.contractType,
    required this.mainContractType,
    required this.amountController,
  });

  @override
  Widget build(BuildContext context) {

    return Column(
      children: [
        _buildAgreementRow(context),
        9.verticalSpace,
        CustomMaterialButton(
          isEnabled: state.isAgree,
          onPressed: () => _showContractSummaryDialog(context, state: state),
          buttonText: 'verify'.tr(),
          borderRadius: 20,
        ),
        37.verticalSpace,
      ],
    );
  }

  Widget _buildAgreementRow(BuildContext context) {
    return Row(
      children: [
        CustomRadioButton(
          isSelected: state.isAgree,
          onChange: (value) => context.read<ContractActivityCubit>().updateIsAgree(value),
        ),
        9.horizontalSpace,
        Text.rich(
          TextSpan(
            text: '${'readAndAgree'.tr()} ',
            style: FontPalette.normal10.copyWith(
              color: context.colorTheme.textPrimary,
            ),
            children: [
              TextSpan(
                text: 'marginAgreement'.tr(),
                style: FontPalette.normal10.copyWith(
                  color: context.theme.primaryColor,
                ),
                recognizer: TapGestureRecognizer()
                  ..onTap = () {
                    showAppInfoBottomSheet(context);
                  },
              ),
            ],
          ),
        ),
      ],
    );
  }

  void _showContractSummaryDialog(
    BuildContext context, {
    required ContractActivityState state,
  }) {
    // if ((int.tryParse(amountController.text.trim()) ?? 0) < 500) {
    //   GPEasyLoading.showToast('minimumAmountHint'.tr());
    //   return;
    // }
    showDialog(
      context: context,
      builder: (_) => BlocProvider.value(
        value: context.read<ContractActivityCubit>(),
        child: ActivitySummaryDialog(
          contractType: contractType,
          mainContractType: mainContractType,
        ),
      ),
    );
  }
}

class _SelectableButton extends StatelessWidget {
  final String title;
  final bool isSelected;
  final VoidCallback onTap;

  const _SelectableButton({
    required this.title,
    required this.isSelected,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {

    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8.gr),
      child: AspectRatio(
        aspectRatio: 2.3,
        child: Container(
          decoration: BoxDecoration(
            gradient: isSelected
                ? const LinearGradient(
                    colors: [
                      Color(0xFF4366DE),
                      Color(0xFF3150BD),
                    ],
                  )
                : null,
            borderRadius: BorderRadius.circular(8.gr),
            border: Border.all(
              color: isSelected ? Colors.transparent : Colors.grey.withNewOpacity(0.3),
            ),
          ),
          child: Center(
            child: AutoSizeText(
              title,
              style: FontPalette.medium14.copyWith(
                color: isSelected ? Colors.white : context.colorTheme.textPrimary,
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ),
      ),
    );
  }
}

class _Divider extends StatelessWidget {
  const _Divider();

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 16.gh),
      child: Divider(
        color: Colors.grey.withNewOpacity(0.1),
        height: 1,
      ),
    );
  }
}
