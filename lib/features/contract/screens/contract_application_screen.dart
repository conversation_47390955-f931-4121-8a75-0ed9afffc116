import 'package:auto_size_text/auto_size_text.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/core/theme/custom_text_theme.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/functions.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/contract/domain/models/contract_calculation/contract_calculation_model.dart';
import 'package:gp_stock_app/features/contract/domain/models/contract_model.dart';
import 'package:gp_stock_app/features/contract/widgets/app_info_bottom_sheet.dart';
import 'package:gp_stock_app/features/contract/widgets/future_funding_widget.dart';
import 'package:gp_stock_app/shared/app/extension/color_extension.dart';
import 'package:gp_stock_app/shared/app/extension/helper.dart';
import 'package:gp_stock_app/shared/logic/account_info/account_info_cubit.dart';
import 'package:gp_stock_app/shared/logic/account_info/account_info_state.dart';
import 'package:gp_stock_app/shared/theme/font_pallette.dart';
import 'package:gp_stock_app/shared/theme/my_color_scheme.dart';
import 'package:gp_stock_app/shared/widgets/buttons/custom_material_button.dart';
import 'package:gp_stock_app/shared/widgets/buttons/custom_radio_button.dart';
import 'package:gp_stock_app/shared/widgets/dropdown/input_dropdown.dart';

import '../../../shared/constants/enums.dart';
import '../../../shared/logic/exchange_rate/exchange_rate_cubit.dart';
import '../../../shared/widgets/shimmer/shimmer_widget.dart';
import '../domain/models/contract_application/contract_application.dart';
import '../logic/contract/contract_cubit.dart';
import '../widgets/amount_row.dart';
import '../widgets/application_summary_dialog.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';

/// Page for applying for normal contract and futures contract
class ContractApplicationScreen extends StatefulWidget {
  const ContractApplicationScreen({super.key, required this.mainContractType, required this.contractType});

  final MainContractType mainContractType;
  final ContractType contractType;
  @override
  State<ContractApplicationScreen> createState() => _ContractApplicationScreenState();
}

class _ContractApplicationScreenState extends State<ContractApplicationScreen> {
  final amountController = TextEditingController();

  @override
  void dispose() {
    amountController.dispose();
    super.dispose();
  }

  @override
  void initState() {
    super.initState();
    final rates = context.read<ExchangeRateCubit>().getCurrencyRateConfig('CNY');
    Helper.afterInit(
        () => context.read<ContractCubit>().getConfigInfo(rates.rate, contractType: widget.mainContractType));
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<ContractCubit, ContractState>(
      listenWhen: (previous, current) {
        return current.selectedAmount != previous.selectedAmount;
      },
      listener: (context, state) {
        amountController.text = state.selectedAmount?.toString() ?? '';
      },
      child: Scaffold(
        backgroundColor: Theme.of(context).scaffoldBackgroundColor,
        appBar: AppBar(
          backgroundColor: Colors.transparent,
          surfaceTintColor: Colors.transparent,
          elevation: 0,
          centerTitle: true,
          iconTheme: IconThemeData(
            color: context.colorTheme.textPrimary,
          ),
          title: Text(
            '${'applyFor'.tr()} [${widget.contractType.title(widget.mainContractType).tr()}]',
            style: FontPalette.medium16.copyWith(
              color: context.colorTheme.textPrimary,
            ),
          ),
        ),
        body: SingleChildScrollView(
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: 16.gw),
            child: BlocBuilder<ContractCubit, ContractState>(
              builder: (context, state) {
                return Column(
                  children: [
                    Container(
                      decoration: BoxDecoration(
                        color: context.theme.cardColor,
                        borderRadius: BorderRadius.circular(8.gr),
                      ),
                      child: Padding(
                        padding: EdgeInsets.symmetric(horizontal: 14.gw, vertical: 12.gh),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            _ContractTypeSection(
                                configInfo: state.configInfo, mainContractType: widget.mainContractType),
                            _Divider(),
                            _PeriodSection(configInfo: state.configInfo),
                            _Divider(),
                            _LeverageSection(
                              configInfo: state.configInfo,
                              selectedContractConfig: state.selectedContractConfig,
                            ),
                            _Divider(),
                            _AmountSection(),
                          ],
                        ),
                      ),
                    ),
                    10.verticalSpace,
                    Container(
                      decoration: BoxDecoration(
                        color: context.theme.cardColor,
                        borderRadius: BorderRadius.circular(8.gr),
                      ),
                      child: Padding(
                        padding: EdgeInsets.symmetric(horizontal: 14.gw, vertical: 12.gh),
                        child: Column(
                          children: [
                            _Divider(),
                            _ContractDetailsSection(
                              amountController: amountController,
                            ),
                          ],
                        ),
                      ),
                    ),
                    30.verticalSpace,
                    _SubmitSection(
                      state: state,
                      amountController: amountController,
                      contractType: widget.contractType,
                      mainContractType: widget.mainContractType,
                    ),
                  ],
                );
              },
            ),
          ),
        ),
      ),
    );
  }
}

class _ContractTypeSection extends StatelessWidget {
  final ContractApplicationData? configInfo;
  final MainContractType mainContractType;
  const _ContractTypeSection({required this.configInfo, required this.mainContractType});
  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'contractType'.tr(),
          style: FontPalette.medium16.copyWith(
            color: context.colorTheme.textPrimary,
          ),
        ),
        12.verticalSpace,
        SizedBox(
          height: 30.gh,
          child: mainContractType == MainContractType.futures
              ? FutureFundingWidget()
              : (configInfo?.ruleMap?.isNotEmpty ?? false)
                  ? ListView.separated(
                      scrollDirection: Axis.horizontal,
                      shrinkWrap: true,
                      itemCount: configInfo?.ruleMap?.length ?? 0,
                      separatorBuilder: (context, index) => 12.horizontalSpace,
                      itemBuilder: (context, index) {
                        final item = configInfo?.ruleMap?[index];
                        return BlocSelector<ContractCubit, ContractState, (RuleMap, String)>(
                          selector: (state) => (state.selectedMarket ?? RuleMap(), state.currency),
                          builder: (context, state) {
                            return _SelectableButton(
                              title: contractMarketTranslation[item?.market ?? '']?.tr() ?? '',
                              isSelected: state.$1.market == item?.market,
                              onTap: () {
                                // Get exchange rate for the market being selected, not current one
                                final newMarketCurrency = switch (item?.market) {
                                  'CN' => 'CNY',
                                  'HK' => 'HKD',
                                  'US' => 'USD',
                                  _ => state.$2,
                                };
                                final newRate =
                                    context.read<ExchangeRateCubit>().getCurrencyRateConfig(newMarketCurrency);
                                context.read<ContractCubit>().selectMarket(item ?? RuleMap(), newRate.rate);
                                context.read<ContractCubit>().calculateContractAmount(newRate.rate);
                              },
                            );
                          },
                        );
                      },
                    )
                  : ShimmerWidget(
                      width: double.infinity,
                      height: 30.gh,
                      child: Container(
                        decoration: BoxDecoration(color: Colors.grey[300]),
                      ),
                    ),
        ),
      ],
    );
  }
}

class _PeriodSection extends StatelessWidget {
  final ContractApplicationData? configInfo;
  const _PeriodSection({required this.configInfo});
  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'period'.tr(),
          style: FontPalette.medium16.copyWith(
            color: context.colorTheme.textPrimary,
          ),
        ),
        12.verticalSpace,
        SizedBox(
          height: 30.gh,
          child: configInfo?.contractConfigMap?.isNotEmpty == true
              ? ListView.separated(
                  scrollDirection: Axis.horizontal,
                  shrinkWrap: true,
                  itemCount: configInfo?.contractConfigMap?.length ?? 0,
                  separatorBuilder: (context, index) => 12.horizontalSpace,
                  itemBuilder: (context, index) {
                    final item = configInfo?.contractConfigMap?[index];
                    return BlocSelector<ContractCubit, ContractState, (int, String)>(
                      selector: (state) => (state.selectedContractConfig?.periodType ?? 0, state.currency),
                      builder: (context, state) {
                        return _SelectableButton(
                          title: getPeriodType(item?.periodType ?? 0),
                          isSelected: state.$1 == item?.periodType,
                          onTap: () {
                            final rates = context.read<ExchangeRateCubit>().getCurrencyRateConfig(state.$2);
                            context.read<ContractCubit>().selectContractConfig(item ?? ContractConfigMap(), rates.rate);
                          },
                        );
                      },
                    );
                  },
                )
              : ShimmerWidget(
                  width: double.infinity,
                  height: 30.gh,
                  child: Container(
                    decoration: BoxDecoration(color: Colors.grey[300]),
                  ),
                ),
        ),
      ],
    );
  }
}

class _LeverageSection extends StatelessWidget {
  final ContractApplicationData? configInfo;
  final ContractConfigMap? selectedContractConfig;
  const _LeverageSection({required this.configInfo, required this.selectedContractConfig});
  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'leverage'.tr(),
          style: FontPalette.medium14.copyWith(
            color: context.colorTheme.textPrimary,
          ),
        ),
        12.verticalSpace,
        configInfo?.contractConfigMap?.isNotEmpty == true
            ? GridView.builder(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 4,
                  crossAxisSpacing: 12,
                  mainAxisSpacing: 8,
                  childAspectRatio: 2.3,
                ),
                itemCount: selectedContractConfig?.configList?.length ?? 0,
                itemBuilder: (context, index) {
                  final item = selectedContractConfig?.configList?[index];
                  return BlocSelector<ContractCubit, ContractState, (ConfigList, String)>(
                    selector: (state) => (state.selectedConfigList ?? ConfigList(), state.currency),
                    builder: (context, state) {
                      return _SelectableButton(
                        title: '${'${item?.multiple}'}${'xTimes'.tr()}',
                        isSelected: state.$1.id == item?.id,
                        onTap: () {
                          final rates = context.read<ExchangeRateCubit>().getCurrencyRateConfig(state.$2);
                          context.read<ContractCubit>().selectConfigList(item ?? ConfigList(), rates.rate);
                        },
                      );
                    },
                  );
                },
              )
            : ShimmerWidget(
                width: double.infinity,
                height: 30.gh,
                child: Container(
                  decoration: BoxDecoration(color: Colors.grey[300]),
                ),
              ),
      ],
    );
  }
}

class _AmountSection extends StatelessWidget {
  const _AmountSection();
  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AccountInfoCubit, AccountInfoState>(
      builder: (context, state) {
        return Column(
          children: [
            AmountRow(
              title: 'availableBalance'.tr(),
              amount: state.accountInfo?.usableCash ?? 0,
              currency: state.accountInfo?.currency ?? '',
            ),
            12.verticalSpace,
            AmountRow(
              title: 'interestCoupon'.tr(),
              amount: state.accountInfo?.interestCash ?? 0,
              currency: state.accountInfo?.currency ?? '',
            ),
          ],
        );
      },
    );
  }
}

class _ContractDetailsSection extends StatelessWidget {
  final TextEditingController amountController;

  const _ContractDetailsSection({required this.amountController});
  @override
  Widget build(BuildContext context) {
    return BlocSelector<
        ContractCubit,
        ContractState,
        (
          int?,
          List<ApplyAmount>,
          ContractModelAmount,
          ContractConfigMap,
          String,
          ContractCalculationModel?,
        )>(
      selector: (state) => (
        state.selectedAmount,
        state.configInfo?.amountList ?? [],
        state.contractModelAmount ?? ContractModelAmount(),
        state.selectedContractConfig ?? ContractConfigMap(),
        state.currency,
        state.contractCalculation,
      ),
      builder: (context, state) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'contractMargin'.tr(),
              style: FontPalette.medium16.copyWith(
                color: context.colorTheme.textPrimary,
              ),
            ),
            12.verticalSpace,
            InputDropdownWidget<ApplyAmount>(
              items: state.$2.map((e) => e).toList(),
              textController: amountController,
              itemBuilder: (dynamic item) => Center(child: Text(item?.applyAmount.toString() ?? '')),
              onTextChanged: (value) {
                try {
                  // if (value.isNotEmpty && int.tryParse(value) != null && int.parse(value) >= 500) {
                  final rates = context.read<ExchangeRateCubit>().getCurrencyRateConfig(state.$5);
                  context.read<ContractCubit>().selectAmount(
                        int.parse(value),
                        rates.rate,
                      );
                  // }
                } catch (e) {
                  // Handle error silently
                }
              },
              onDropdownChanged: (value) {
                final rates = context.read<ExchangeRateCubit>().getCurrencyRateConfig(state.$5);
                context.read<ContractCubit>().selectAmount(
                      value?.applyAmount ?? 0,
                      rates.rate,
                    );
              },
            ),
            16.verticalSpace,
            AmountRow(
              title: 'totalMargin'.tr(),
              amount: state.$3.totalTadingFunds ?? 0,
              currency: state.$5,
            ),
            12.verticalSpace,
            AmountRow(
              title: 'warningLine'.tr(),
              amount: state.$3.lossWarningLine ?? 0,
              currency: state.$5,
            ),
            12.verticalSpace,
            AmountRow(
              title: 'stopLossLine'.tr(),
              amount: state.$3.lossFlatLine ?? 0,
              currency: state.$5,
            ),
            12.verticalSpace,
            AmountRow(
              title: 'interestRate'.tr(),
              amount: state.$6?.rateAmount ?? 0,
              currency: '${state.$5} / ${contractTypeTranslation[state.$4.periodType ?? 0]?.$2.tr() ?? ''}',
            ),
            12.verticalSpace,
            AmountRow(
              title: 'interestReduceAmount'.tr(),
              amount: state.$6?.deductInterestCashCNY ?? 0,
              isCurrency: true,
              currency: 'CNY',
            ),
            12.verticalSpace,
            AmountRow(
              title: 'actualPaymentAmount'.tr(),
              amount: state.$6?.deductCanUseCashCNY ?? 0,
              isCurrency: true,
              prefix: state.$5 != 'CNY' ? '≈ ' : '',
              currency: 'CNY',
            ),
            12.verticalSpace,
          ],
        );
      },
    );
  }
}

class _SubmitSection extends StatelessWidget {
  final ContractState state;
  final TextEditingController amountController;
  final ContractType contractType;
  final MainContractType mainContractType;
  const _SubmitSection(
      {required this.state,
      required this.amountController,
      required this.contractType,
      required this.mainContractType});
  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        GestureDetector(
          onTap: () => context.read<ContractCubit>().updateIsAgree(!state.isAgree),
          child: Row(
            children: [
              CustomRadioButton(
                isSelected: state.isAgree,
                onChange: (value) => context.read<ContractCubit>().updateIsAgree(value),
              ),
              9.horizontalSpace,
              Text.rich(
                TextSpan(
                  text: '${'readAndAgree'.tr()} ',
                  style: FontPalette.normal10.copyWith(
                    color: context.colorTheme.textPrimary,
                  ),
                  children: [
                    TextSpan(
                      text: 'marginAgreement'.tr(),
                      style: FontPalette.normal10.copyWith(
                        color: context.theme.primaryColor,
                      ),
                      recognizer: TapGestureRecognizer()
                        ..onTap = () {
                          showAppInfoBottomSheet(context);
                        },
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
        9.verticalSpace,
        CustomMaterialButton(
          isEnabled: state.isAgree,
          onPressed: () {
            // if ((int.tryParse(amountController.text) ?? 0) < 500) {
            //   GPEasyLoading.showToast('minimumAmountHint'.tr());
            //   return;
            // }
            showDialog(
              context: context,
              builder: (_) => BlocProvider.value(
                value: context.read<ContractCubit>(),
                child: ApplicationSummaryDialog(
                  contractType: contractType,
                  mainContractType: mainContractType,
                ),
              ),
            );
          },
          buttonText: 'verify'.tr(),
          borderRadius: 20,
        ),
        37.verticalSpace,
      ],
    );
  }
}

class _SelectableButton extends StatelessWidget {
  final String title;
  final bool isSelected;
  final VoidCallback onTap;

  const _SelectableButton({
    required this.title,
    required this.isSelected,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: AspectRatio(
        aspectRatio: 2.3,
        child: Container(
          decoration: BoxDecoration(
            gradient: isSelected
                ? LinearGradient(
                    colors: [
                      const Color(0xFF4366DE),
                      const Color(0xFF3150BD),
                    ],
                  )
                : null,
            borderRadius: BorderRadius.circular(8.gr),
            border: Border.all(
              color: isSelected ? Colors.transparent : Colors.grey.withNewOpacity(0.3),
            ),
          ),
          child: Center(
            child: Padding(
              padding: EdgeInsets.symmetric(horizontal: 4.gw),
              child: AutoSizeText(
                title,
                minFontSize: 12,
                style: FontPalette.medium14.copyWith(
                  color: isSelected ? Colors.white : context.colorTheme.textPrimary,
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}

class _Divider extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 16.gh),
      child: Divider(
        color: Colors.grey.withAlpha(26),
        height: 1,
      ),
    );
  }
}
