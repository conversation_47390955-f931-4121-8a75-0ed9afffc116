import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/core/theme/custom_text_theme.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/contract/domain/filter_constants.dart';
import 'package:gp_stock_app/features/contract/domain/models/date_filter.dart';
import 'package:gp_stock_app/features/contract/domain/models/transaction_type.dart';
import 'package:gp_stock_app/features/contract/logic/fund_records/fund_records_cubit.dart';
import 'package:gp_stock_app/shared/logic/account_info/account_info_cubit.dart';
import 'package:gp_stock_app/shared/theme/font_pallette.dart';
import 'package:gp_stock_app/shared/theme/my_color_scheme.dart';
import 'package:gp_stock_app/shared/widgets/buttons/custom_material_button.dart';
import 'package:gp_stock_app/shared/widgets/dropdown/app_dropdown.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';

class FundRecordFilterSheet extends StatelessWidget {
  const FundRecordFilterSheet({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocSelector<FundRecordsCubit, FundRecordsState,
        ({DateFilter? dateFilter, TransactionType? transactionType})>(
      selector: (state) => (dateFilter: state.dateFilter, transactionType: state.transactionType),
      builder: (context, state) {
        return Container(
          padding: EdgeInsets.symmetric(horizontal: 16),
          color: context.theme.cardColor,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            spacing: 10,
            children: [
              const SizedBox(height: 10, width: double.infinity),
              Text(
                "fundType".tr(),
                style: FontPalette.normal13,
              ),
              AppDropdown<TransactionType>(
                selected: state.transactionType,
                hintText: 'selectFundType'.tr(),
                items: FilterConstants.contractTypes
                    .map(
                      (e) => DropdownMenuItem(
                        value: e,
                        child: Text(e.label.tr()),
                      ),
                    )
                    .toList(),
                onChanged: (value) {
                  context.read<FundRecordsCubit>().setTranscationType(value);
                },
              ),
              Text(
                'queryTime'.tr(),
                style: FontPalette.normal13,
              ),
              Wrap(
                spacing: 8.gw,
                runSpacing: 8.gh,
                children: FilterConstants.filters
                    .map(
                      (e) => FilterChip(
                        label: Text(e.tr.tr()),
                        selected: state.dateFilter == e,
                        showCheckmark: false,
                        onSelected: (selected) {
                          if (selected) {
                            context.read<FundRecordsCubit>().setDateFilter(e);
                          } else {
                            context.read<FundRecordsCubit>().setDateFilter(null);
                          }
                        },
                        labelStyle: FontPalette.normal13.copyWith(
                          color: state.dateFilter == e ? Colors.white : context.colorTheme.textRegular,
                        ),
                        surfaceTintColor: Colors.transparent,
                        shadowColor: Colors.transparent,
                        color: WidgetStateProperty.all(
                            state.dateFilter == e ? context.theme.primaryColor : Colors.transparent),
                        backgroundColor: Colors.transparent,
                        padding: EdgeInsets.symmetric(horizontal: 2),
                        selectedShadowColor: Colors.transparent,
                        selectedColor: context.theme.scaffoldBackgroundColor,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(10.gr),
                          side: BorderSide(
                            color: context.theme.dividerColor,
                            width: 1,
                          ),
                        ),
                      ),
                    )
                    .toList(),
              ),
              Row(
                children: [
                  Expanded(
                    child: CustomMaterialButton(
                      onPressed: () {
                        context.read<FundRecordsCubit>()
                          ..setTranscationType(null)
                          ..setDateFilter(null);
                      },
                      isOutLined: true,
                      buttonText: 'reset'.tr(),
                    ),
                  ),
                  SizedBox(width: 10.gw),
                  Expanded(
                    child: CustomMaterialButton(
                      onPressed: () {
                        final commentAssetId = context.read<AccountInfoCubit>().state.accountInfo?.assetId;
                        context.read<FundRecordsCubit>().getAssetRecord(
                              contractId: null,
                              isLoadMore: false,
                              commentAssetId: commentAssetId?.toString(),
                            );
                        Navigator.pop(context);
                      },
                      buttonText: 'submit'.tr(),
                    ),
                  )
                ],
              ),
              const SizedBox(height: 30, width: double.infinity),
            ],
          ),
        );
      },
    );
  }
}
