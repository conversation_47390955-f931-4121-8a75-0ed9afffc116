import 'package:flutter/material.dart';
import 'package:gp_stock_app/config/flavors/app_config.dart';

void setupGoldenAConfig() {
  AppConfig(
    flavor: Flavor.goldenA,
    appName: 'GoldenA',
    siteId: "1",
    environment: "prod",
    baseUrl: 'https://h5.rgshunht.com',
    marketWsUrl: 'wss://h5.rgshunht.com',
    inviteLinkUrl: 'https://h5.rgshunht.com/#/?inviteCode=',
    // GoldenA primary color
    primaryColor: const Color(0xFFFFD700),
    // AES encryption key for GoldenA flavor
    encryptionKey: '8JUOEEGjDsmrl30P',
    wangYiCaptchaKey: '7650f145f0824ba6973d99d43a99d15c',
    ossUrls: [
      "https://rs-1337543130.cos.ap-shanghai.myqcloud.com/prod/1/app_api.json",
      "https://bj-1337543130.cos.ap-beijing.myqcloud.com/prod/1/app_api.json",
      "https://gz-1337543130.cos.ap-guangzhou.myqcloud.com/prod/1/app_api.json",
      "https://cq-1337543130.cos.ap-chongqing.myqcloud.com/prod/1/app_api.json",
      "https://xg-1337543130.cos.ap-hongkong.myqcloud.com/prod/1/app_api.json",
    ],
  );
}
