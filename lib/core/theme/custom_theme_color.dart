
import 'package:flutter/material.dart';

/// 自定义颜色主题，用于配合 [CustomTextTheme] 构建统一配色方案
/// Custom color theme used to define unified colors across the app
///
/// 可通过 [context.colorTheme] 获取当前主题颜色集
/// Access via [context.colorTheme] in your widgets
class CustomColorTheme extends ThemeExtension<CustomColorTheme> {
  final Color textPrimary;     // 主要文字 Main text
  final Color textRegular;     // 常规文字 Secondary text
  final Color tabActive;       // 激活标签页文字 Tab active color
  final Color tabInactive;     // 非激活标签页文字 Tab inactive color
  final Color buttonPrimary;   // 按钮主色 Button primary color
  final Color buttonSecondary; // 次按钮颜色 Secondary button color
  final Color stockRed;        // 股票红色 Stock red
  final Color stockGreen;      // 股票绿色 Stock green
  final Color pending;         // 中性状态（待审核等） Neutral status (e.g. pending)

  const CustomColorTheme({
    required this.textPrimary,
    required this.textRegular,
    required this.tabActive,
    required this.tabInactive,
    required this.buttonPrimary,
    required this.buttonSecondary,
    required this.stockRed,
    required this.stockGreen,
    required this.pending,
  });

  /// 亮色主题配色 Light mode color palette
  static const CustomColorTheme gpLightA = CustomColorTheme(
    textPrimary: Color(0xFF8C6450),
    textRegular: Color(0xFFCBC0AF),
    tabActive: Colors.black,
    tabInactive: Color(0xFFBDBDBD),
    buttonPrimary: Colors.white,
    buttonSecondary: Color(0xFF000000),
    stockRed: Color(0xFFC92C31),
    stockGreen: Color(0xFF1CB570),
    pending: Color(0xFFF8BB18),
  );

  /// 暗黑主题配色 Dark mode color palette
  static const CustomColorTheme gpDarkA = CustomColorTheme(
    textPrimary: Color(0xFF8C6450),
    textRegular: Color(0xFFCBC0AF),
    tabActive: Colors.white,
    tabInactive: Color(0xFF666666),
    buttonPrimary: Color(0xFF1F1911),
    buttonSecondary: Colors.white,
    stockRed: Color(0xFFC92C31),
    stockGreen: Color(0xFF1CB570),
    pending: Color(0xFFF8BB18),
  );

  /// 拷贝当前配色，可用于动态生成主题
  @override
  CustomColorTheme copyWith({
    Color? textPrimary,
    Color? textRegular,
    Color? tabActive,
    Color? tabInactive,
    Color? buttonPrimary,
    Color? buttonSecondary,
    Color? stockRed,
    Color? stockGreen,
    Color? pending,
  }) {
    return CustomColorTheme(
      textPrimary: textPrimary ?? this.textPrimary,
      textRegular: textRegular ?? this.textRegular,
      tabActive: tabActive ?? this.tabActive,
      tabInactive: tabInactive ?? this.tabInactive,
      buttonPrimary: buttonPrimary ?? this.buttonPrimary,
      buttonSecondary: buttonSecondary ?? this.buttonSecondary,
      stockRed: stockRed ?? this.stockRed,
      stockGreen: stockGreen ?? this.stockGreen,
      pending: pending ?? this.pending,
    );
  }

  /// 用于主题切换动画渐变插值
  @override
  CustomColorTheme lerp(ThemeExtension<CustomColorTheme>? other, double t) {
    if (other is! CustomColorTheme) return this;
    return CustomColorTheme(
      textPrimary: Color.lerp(textPrimary, other.textPrimary, t)!,
      textRegular: Color.lerp(textRegular, other.textRegular, t)!,
      tabActive: Color.lerp(tabActive, other.tabActive, t)!,
      tabInactive: Color.lerp(tabInactive, other.tabInactive, t)!,
      buttonPrimary: Color.lerp(buttonPrimary, other.buttonPrimary, t)!,
      buttonSecondary: Color.lerp(buttonSecondary, other.buttonSecondary, t)!,
      stockRed: Color.lerp(stockRed, other.stockRed, t)!,
      stockGreen: Color.lerp(stockGreen, other.stockGreen, t)!,
      pending: Color.lerp(pending, other.pending, t)!,
    );
  }
}
