import 'dart:math';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/shared/theme/font_pallette.dart';
import 'package:gp_stock_app/shared/theme/my_color_scheme.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';

class WithdrawalPasswordKeyboard extends StatelessWidget {
  final TextEditingController controller;
  final double bottomPadding;
  final ValueChanged<String>? onChanged;
  final VoidCallback? onSubmit;

  const WithdrawalPasswordKeyboard({
    super.key,
    required this.controller,
    this.bottomPadding = 0,
    this.onChanged,
    this.onSubmit,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.only(bottom: bottomPadding),
      decoration: BoxDecoration(
        color: context.theme.cardColor,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20.gr)),
        boxShadow: [
          BoxShadow(
            color: context.theme.shadowColor,
            blurRadius: 10,
            spreadRadius: 1,
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          16.verticalSpace,
          CustomPinKeyboard(
            controller: controller,
            onChanged: onChanged,
            onSubmit: onSubmit,
          ),
        ],
      ),
    );
  }
}

class CustomPinKeyboard extends StatelessWidget {
  final TextEditingController controller;
  final int length;
  final ValueChanged<String>? onChanged;
  final VoidCallback? onSubmit;

  const CustomPinKeyboard({
    super.key,
    required this.controller,
    this.length = 6,
    this.onChanged,
    this.onSubmit,
  });

  @override
  Widget build(BuildContext context) {
    final random = Random();
    List<String> keys = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9']..shuffle(random);

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        20.verticalSpace,
        GridView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          padding: EdgeInsets.symmetric(horizontal: 16.gw),
          gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 3,
            mainAxisSpacing: 16.gh,
            crossAxisSpacing: 16.gw,
            childAspectRatio: 2,
          ),
          itemCount: 12,
          itemBuilder: (context, index) {
            if (index == 9) {
              return const SizedBox.shrink(); // Empty space
            }
            if (index == 11) {
              return FilledButton(
                onPressed: () {
                  if (controller.text.isNotEmpty) {
                    controller.text = controller.text.substring(0, controller.text.length - 1);
                    onChanged?.call(controller.text);
                    HapticFeedback.lightImpact();
                  }
                },
                child: Icon(
                  Icons.backspace_outlined,
                  color: context.theme.cardColor,
                ),
              );
            }

            // Map the 10 numbers to the 10 available slots (0-8, 10)
            final keyIndex = index < 9 ? index : 9; // Index 10 uses keys[9]
            final number = keys[keyIndex];

            return OutlinedButton(
              onPressed: () {
                if (controller.text.length < length) {
                  controller.text += number;
                  onChanged?.call(controller.text);
                  HapticFeedback.lightImpact();

                  if (controller.text.length == length) {
                    onSubmit?.call();
                  }
                }
              },
              
              child: Text(
                number,
                style: FontPalette.semiBold18.copyWith(
                  color: context.theme.primaryColor,
                ),
              ),
            );
          },
        ),
        24.verticalSpace,
      ],
    );
  }
}
