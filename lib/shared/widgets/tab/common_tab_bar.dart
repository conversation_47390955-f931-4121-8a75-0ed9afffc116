import 'package:flutter/material.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/shared/widgets/tab_indicator/tc_underline_tab_indicator.dart';

class CommonTabBar extends StatefulWidget {
  final List<String> data;
  final int currentIndex; // 新增：外部传入
  final EdgeInsets? padding;
  final EdgeInsets? labelPadding;
  final ValueChanged<int>? onTap;
  final Color backgroundColor;
  final double height;
  final bool isScrollable;
  final TabAlignment? tabAlignment;

  const CommonTabBar({
    super.key,
    required this.data,
    this.currentIndex = 0,
    this.padding,
    this.labelPadding,
    this.onTap,
    this.backgroundColor = Colors.white,
    this.height = 40,
    this.isScrollable = true,
    this.tabAlignment,
  }) : assert(currentIndex >= 0 && currentIndex < data.length);

  @override
  State<CommonTabBar> createState() => _CommonTabBarState();
}

class _CommonTabBarState extends State<CommonTabBar> with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(
      length: widget.data.length,
      vsync: this,
      initialIndex: widget.currentIndex,
    );
    _tabController.addListener(_handleTabChange);
  }

  @override
  void didUpdateWidget(covariant CommonTabBar old) {
    super.didUpdateWidget(old);

    // 1. 如果 data 长度变化，重新创建 controller
    if (old.data.length != widget.data.length) {
      _tabController
        ..removeListener(_handleTabChange)
        ..dispose();
      _tabController = TabController(
        length: widget.data.length,
        vsync: this,
        initialIndex: widget.currentIndex.clamp(0, widget.data.length - 1),
      )..addListener(_handleTabChange);
    }

    // 2. 如果外部 currentIndex 变化，同步到 TabController
    if (old.currentIndex != widget.currentIndex && widget.currentIndex != _tabController.index) {
      _tabController.animateTo(widget.currentIndex);
    }
  }

  void _handleTabChange() {
    // 只有用户手指拖动/点击才触发，避免外部 animateTo 再反吹
    if (_tabController.indexIsChanging) {
      widget.onTap?.call(_tabController.index);
    }
  }

  @override
  void dispose() {
    _tabController
      ..removeListener(_handleTabChange)
      ..dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: widget.padding,
      color: widget.backgroundColor,
      height: widget.height,
      child: TabBar(
        controller: _tabController,
        isScrollable: widget.isScrollable,
        tabAlignment: widget.tabAlignment ?? TabAlignment.center,
        overlayColor: WidgetStateProperty.all(Colors.transparent),
        dividerColor: Colors.transparent,
        labelPadding: widget.labelPadding ?? EdgeInsets.symmetric(horizontal: 15.gw),
        labelStyle: TextStyle(
          fontSize: 14.gsp,
          color: context.theme.primaryColor,
          fontWeight: FontWeight.w500,
        ),
        unselectedLabelStyle: TextStyle(
          fontSize: 14.gsp,
          color: context.colorTheme.textRegular,
          fontWeight: FontWeight.w400,
        ),
        indicator: TCUnderlineTabIndicator(
          indicatorWidth: widget.data[_tabController.index].length / 2 * 12.gw,
          isRound: true,
          insets: const EdgeInsets.fromLTRB(0, 0, 0, 6),
          borderSide: BorderSide(width: 2.0, color: context.theme.primaryColor),
        ),
        tabs: widget.data.map((str) => Tab(text: str)).toList(),
      ),
    );
  }
}
