import 'package:flutter/material.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';

import '../../constants/enums.dart';
import '../../theme/my_color_scheme.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';

void showAppSnackBar(
  String message,
  BuildContext context, {
  IconData? icon,
  Color? color,
  Color backgroundColor = Colors.black,
  bool isWarning = false,
  bool isInfinite = false,
  double bottomPadding = 50,
  SnackBarType snackBarType = SnackBarType.validation,
}) {
  ScaffoldMessenger.of(context)
    ..hideCurrentSnackBar()
    ..showSnackBar(
      SnackBar(
        elevation: 0,
        backgroundColor: Colors.transparent,
        content: Container(
          constraints: BoxConstraints(minHeight: 40.gh, maxHeight: 50.gh),
          margin: EdgeInsets.only(bottom: bottomPadding - 40),
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 3),
          decoration: BoxDecoration(
            color: getSnackBarColor(snackBarType, context),
            borderRadius: BorderRadius.circular(8.gr),
          ),
          child: Row(
            children: [
              if (icon != null) ...[
                Icon(
                  icon,
                  color: color ?? Colors.white,
                ),
                8.horizontalSpace,
              ],
              Expanded(
                child: Text(
                  message,
                  style: TextStyle(
                    color: color ?? Colors.white,
                    fontSize: 14.gsp,
                  ),
                ),
              ),
            ],
          ),
        ),
        duration: isInfinite ? const Duration(days: 1) : const Duration(seconds: 2),
      ),
    );
}

Color getSnackBarColor(SnackBarType snackBarType, BuildContext context) {
  switch (snackBarType) {
    case SnackBarType.success:
      return Colors.green;
    case SnackBarType.warning:
      return Colors.orange;
    case SnackBarType.error:
      return Colors.red;
    default:
      return context.theme.primaryColor;
  }
}
