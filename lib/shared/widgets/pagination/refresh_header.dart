import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

import '../../theme/font_pallette.dart';
import '../../theme/my_color_scheme.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';

class RefreshHeader extends StatelessWidget {
  final bool isShowIndicator;
  const RefreshHeader({super.key, this.isShowIndicator = true});

  ///
  /// [showText]
  Widget _prompt(String showText, BuildContext context, {bool isShowIndicator = false}) {
    return Column(
      children: [
        Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (isShowIndicator) ...[
              SizedBox(
                  width: 15.gw,
                  height: 15.gw,
                  child: CircularProgressIndicator.adaptive(
                    strokeWidth: 1.gw,
                  )),
              10.horizontalSpace
            ],
            Text(
              showText.tr(),
              style: FontPalette.bold12
                  .copyWith(color: context.colorTheme.textRegular, fontFamily: 'Akzidenz-Grotesk'),
            ),
          ],
        ),
      ],
    );
  }

  /// [showText]
  Widget _hint(String showText, BuildContext context, {bool isShowIndicator = false}) {
    return Column(
      children: [
        _prompt(showText, context, isShowIndicator: isShowIndicator),
        SizedBox(height: 8.gh) /*, _prompt(showText)*/
      ],
    );
  }

  CustomHeader _header(BuildContext context, {bool isShowIndicator = false}) {
    Widget body = _hint('pullToRefresh', context);
    return CustomHeader(
      builder: (context, mode) {
        if (mode == RefreshStatus.idle) {
          body = _hint('pullToRefresh', context, isShowIndicator: isShowIndicator);
        } else if (mode == RefreshStatus.refreshing) {
          body = _hint('refreshing', context, isShowIndicator: isShowIndicator);
        } else if (mode == RefreshStatus.failed) {
          body = _hint('loadFailed', context, isShowIndicator: isShowIndicator);
        } else if (mode == RefreshStatus.canRefresh) {
          body = _hint('releaseToRefresh', context, isShowIndicator: isShowIndicator);
        } else if (mode == RefreshStatus.completed) {
          body = _hint('refreshSuccess', context, isShowIndicator: isShowIndicator);
        } else {
          body = _hint('loadError', context, isShowIndicator: isShowIndicator);
        }
        return body;
      },
    );
  }

  @override
  Widget build(BuildContext context) => _header(context, isShowIndicator: isShowIndicator);
}
