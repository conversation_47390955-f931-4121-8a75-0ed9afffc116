import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';

import '../../constants/assets.dart';
import '../../theme/font_pallette.dart';
import '../../theme/my_color_scheme.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/shared/app/extension/color_extension.dart';

enum TextFieldBorderType {
  outline,
  underline,
  none,
}

class TextFieldWidget extends StatefulWidget {
  const TextFieldWidget({
    super.key,
    this.labelText,
    this.topLabelText,
    this.hintText,
    this.textStyle,
    this.hintStyle,
    this.isHint = false,
    this.textInputType,
    this.textCapitalization = TextCapitalization.none,
    this.inputFormatters,
    this.textDirection,
    this.maxLines,
    this.maxLength,
    this.minLines,
    this.counterText,
    this.textInputAction,
    this.hideCounterText = false,
    this.controller,
    this.borderColor,
    this.prefixIcon,
    this.suffixIcon,
    this.suffixIconConstraints,
    this.autoValidateMode,
    this.validator,
    this.errorText,
    this.focusNode,
    this.enabled,
    this.isDense = false,
    this.showBorderSide = true,
    this.contentPadding,
    this.constraints,
    this.readOnly,
    this.onSaved,
    this.onChanged,
    this.onTap,
    this.obscureText,
    this.autofillHints,
    this.floatingLabelBehavior,
    this.fillColor,
    this.errorColor = const Color(0xFFC92C31),
    this.fontSize,
    this.hintSize,
    this.textAlign = TextAlign.start,
    this.fontColor,
    this.textFieldHeight,
    this.isRequired = false,
    this.passwordIcon = false,
    this.onEditingComplete,
    this.borderType = TextFieldBorderType.outline,
    this.decoration,
  });

  final String? labelText;
  final String? topLabelText;
  final String? hintText;
  final TextStyle? textStyle;
  final TextStyle? hintStyle;
  final bool isHint;
  final TextInputType? textInputType;
  final TextInputAction? textInputAction;
  final TextCapitalization textCapitalization;
  final List<TextInputFormatter>? inputFormatters;
  final TextDirection? textDirection;
  final int? maxLines, maxLength, minLines;
  final String? counterText;
  final bool hideCounterText;
  final Color? borderColor;
  final Widget? prefixIcon;
  final Widget? suffixIcon;
  final BoxConstraints? suffixIconConstraints;
  final TextEditingController? controller;
  final AutovalidateMode? autoValidateMode;
  final String? Function(String?)? validator;
  final FocusNode? focusNode;
  final bool? enabled;
  final bool? readOnly;
  final bool? isDense;
  final bool showBorderSide;
  final Function(String?)? onSaved;
  final Function(String?)? onChanged;
  final Function()? onTap;
  final BoxConstraints? constraints;
  final FloatingLabelBehavior? floatingLabelBehavior;
  final EdgeInsetsGeometry? contentPadding;
  final bool? obscureText;
  final String? errorText;
  final Color? fontColor, fillColor, errorColor;
  final double? fontSize, hintSize, textFieldHeight;
  final TextAlign textAlign;
  final bool isRequired;
  final List<String>? autofillHints;
  final VoidCallback? onEditingComplete;
  final bool passwordIcon;
  final TextFieldBorderType borderType;
  final InputDecoration? decoration;

  @override
  TextFieldWidgetState createState() => TextFieldWidgetState();
}

class TextFieldWidgetState extends State<TextFieldWidget> {
  late FocusNode _focusNode;
  late bool _isObscure;

  @override
  void initState() {
    super.initState();
    _focusNode = widget.focusNode ?? FocusNode();
    _focusNode.addListener(_handleFocusChange);
    _isObscure = widget.obscureText ?? false;
  }

  @override
  void dispose() {
    _focusNode.removeListener(_handleFocusChange);
    if (widget.focusNode == null) {
      _focusNode.dispose();
    }
    super.dispose();
  }

  void _handleFocusChange() {
    setState(() {});
  }

  void _toggleObscureText() {
    setState(() {
      _isObscure = !_isObscure;
    });
  }

  @override
  Widget build(BuildContext context) {
    // Get input decoration based on border type
    final inputDecoration = widget.decoration ?? _getInputDecoration(context);

    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (widget.topLabelText != null)
          Column(
            children: [
              Text(
                widget.topLabelText ?? '',
                style: FontPalette.normal14,
              ),
              8.verticalSpace,
            ],
          ),
        // Use intrinsic height if no specific height is provided
        widget.textFieldHeight == null
            ? TextFormField(
                controller: widget.controller,
                textAlign: widget.textAlign,
                textInputAction: widget.textInputAction,
                autofillHints: widget.autofillHints,
                decoration: inputDecoration,
                keyboardType: widget.textInputType ?? TextInputType.text,
                textCapitalization: widget.textCapitalization,
                inputFormatters: widget.inputFormatters,
                textDirection: widget.textDirection ?? TextDirection.ltr,
                maxLines: widget.maxLines ?? 1,
                minLines: widget.minLines ?? 1,
                maxLength: widget.maxLength,
                autovalidateMode: widget.autoValidateMode ?? AutovalidateMode.disabled,
                validator: widget.validator,
                focusNode: _focusNode,
                style: FontPalette.normal14.copyWith(
                  color: widget.fontColor ?? context.colorTheme.textPrimary,
                ),
                enabled: widget.enabled ?? true,
                readOnly: widget.readOnly ?? false,
                onSaved: widget.onSaved,
                onChanged: widget.onChanged,
                onEditingComplete: widget.onEditingComplete,
                onTap: widget.onTap,
                obscureText: _isObscure,
              )
            : SizedBox(
                height: widget.textFieldHeight!.gh,
                child: TextFormField(
                  controller: widget.controller,
                  textAlign: widget.textAlign,
                  textInputAction: widget.textInputAction,
                  autofillHints: widget.autofillHints,
                  decoration: inputDecoration,
                  keyboardType: widget.textInputType ?? TextInputType.text,
                  textCapitalization: widget.textCapitalization,
                  inputFormatters: widget.inputFormatters,
                  textDirection: widget.textDirection ?? TextDirection.ltr,
                  maxLines: widget.maxLines ?? 1,
                  minLines: widget.minLines ?? 1,
                  maxLength: widget.maxLength,
                  autovalidateMode: widget.autoValidateMode ?? AutovalidateMode.disabled,
                  validator: widget.validator,
                  focusNode: _focusNode,
                  style: FontPalette.normal14.copyWith(
                    color: widget.fontColor ?? context.colorTheme.textPrimary,
                  ),
                  enabled: widget.enabled ?? true,
                  readOnly: widget.readOnly ?? false,
                  onSaved: widget.onSaved,
                  onChanged: widget.onChanged,
                  onEditingComplete: widget.onEditingComplete,
                  onTap: widget.onTap,
                  obscureText: _isObscure,
                ),
              ),
      ],
    );
  }

  InputDecoration _getInputDecoration(BuildContext context) {
    // Base decoration with adjusted padding for password fields
    InputDecoration decoration = InputDecoration(
      contentPadding: widget.contentPadding,
      prefixIcon: widget.prefixIcon != null
          ? Padding(
              padding: EdgeInsets.symmetric(horizontal: 8.gw),
              child: SizedBox(width: 18.gw, height: 18.gh, child: widget.prefixIcon),
            )
          : null,
      floatingLabelBehavior: widget.floatingLabelBehavior,
      counterText: widget.hideCounterText ? '' : widget.counterText,
      fillColor: widget.fillColor ?? context.theme.inputDecorationTheme.fillColor,
      filled: true,
      suffixIcon: widget.passwordIcon
          ? SizedBox(
              width: 32.gw, // Reduced width for password icon
              child: IconButton(
                padding: EdgeInsets.zero,
                onPressed: _toggleObscureText,
                icon: _isObscure
                    ? SvgPicture.asset(Assets.eyeShow,
                        width: 18.gw, // Slightly smaller icon
                        height: 18.gh)
                    : SvgPicture.asset(Assets.eyeHide,
                        width: 12.gw, // Slightly smaller icon
                        height: 12.gh),
              ),
            )
          : widget.suffixIcon,
      suffixIconConstraints: widget.passwordIcon
          ? BoxConstraints(
              minWidth: 32.gw, // Smaller constraints for password icon
              minHeight: 32.gh,
            )
          : (widget.suffixIconConstraints ??
              BoxConstraints(
                minWidth: 40.gw,
                minHeight: 40.gh,
              )),
      hintText: widget.isHint ? widget.labelText : widget.hintText,
      hintStyle:
          widget.hintStyle ?? TextStyle(fontSize: (widget.hintSize ?? 14).gsp, color: context.colorTheme.textRegular),
      isDense: widget.isDense,
      constraints: widget.constraints,
    );

    // Apply border based on borderType
    switch (widget.borderType) {
      case TextFieldBorderType.outline:
        decoration = decoration.copyWith(
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12.gr),
            borderSide: widget.showBorderSide ? BorderSide(color: Colors.grey.withNewOpacity(0.25)) : BorderSide.none,
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12.gr),
            borderSide:
                widget.showBorderSide ? BorderSide(color: context.theme.primaryColor.withAlpha(128)) : BorderSide.none,
          ),
          errorBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12.gr),
            borderSide:
                widget.showBorderSide ? BorderSide(color: context.colorTheme.stockRed.withAlpha(128)) : BorderSide.none,
          ),
          focusedErrorBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12.gr),
            borderSide:
                widget.showBorderSide ? BorderSide(color: context.colorTheme.stockRed.withAlpha(128)) : BorderSide.none,
          ),
          disabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12.gr),
            borderSide: widget.showBorderSide
                ? BorderSide(
                    color: widget.enabled == false ? Colors.transparent : context.colorTheme.textRegular.withAlpha(128))
                : BorderSide.none,
          ),
        );
        break;
      case TextFieldBorderType.underline:
        decoration = decoration.copyWith(
          enabledBorder: UnderlineInputBorder(
            borderSide: widget.showBorderSide ? BorderSide(color: Colors.grey.withNewOpacity(0.25)) : BorderSide.none,
          ),
          focusedBorder: UnderlineInputBorder(
            borderSide:
                widget.showBorderSide ? BorderSide(color: context.theme.primaryColor.withAlpha(128)) : BorderSide.none,
          ),
          errorBorder: UnderlineInputBorder(
            borderSide:
                widget.showBorderSide ? BorderSide(color: context.colorTheme.stockRed.withAlpha(128)) : BorderSide.none,
          ),
          focusedErrorBorder: UnderlineInputBorder(
            borderSide:
                widget.showBorderSide ? BorderSide(color: context.colorTheme.stockRed.withAlpha(128)) : BorderSide.none,
          ),
          disabledBorder: UnderlineInputBorder(
            borderSide: widget.showBorderSide
                ? BorderSide(
                    color: widget.enabled == false ? Colors.transparent : context.colorTheme.textRegular.withAlpha(128))
                : BorderSide.none,
          ),
        );
        break;
      case TextFieldBorderType.none:
        decoration = decoration.copyWith(
          enabledBorder: InputBorder.none,
          focusedBorder: InputBorder.none,
          errorBorder: InputBorder.none,
          focusedErrorBorder: InputBorder.none,
          disabledBorder: InputBorder.none,
          border: InputBorder.none,
        );
        break;
    }

    return decoration;
  }
}
