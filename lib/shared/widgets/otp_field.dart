// widgets/otp/otp_field.dart
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/core/utils/validators.dart';
import 'package:gp_stock_app/shared/constants/assets.dart';
import 'package:gp_stock_app/shared/widgets/buttons/common_button.dart';
import 'package:gp_stock_app/shared/widgets/buttons/custom_material_button.dart';
import 'package:gp_stock_app/shared/widgets/text_fields/text_field_widget.dart';

import '../../features/account/logic/otp/otp_state.dart';
import '../theme/font_pallette.dart';
import '../theme/my_color_scheme.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';

class OtpField extends StatelessWidget {
  final TextEditingController mobileController;
  final TextEditingController codeController;
  final VoidCallback onSendCode;
  final OtpState otpState;
  final bool readOnlyMobile;

  const OtpField({
    super.key,
    required this.mobileController,
    required this.codeController,
    required this.onSendCode,
    required this.otpState,
    this.readOnlyMobile = false,
  });

  @override
  Widget build(BuildContext context) {
    final List<Widget> children = [];

    // Add mobile field or text
    if (readOnlyMobile) {
      children.add(
        Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            SizedBox(
              width: 80.gw,
              child: Text(
                'currentPhone'.tr(),
                style: FontPalette.medium14,
              ),
            ),
            Expanded(
              child: Text(
                mobileController.text,
                style: FontPalette.normal14.copyWith(
                  color: context.colorTheme.textRegular,
                ),
              ),
            ),
          ],
        ),
      );
    } else {
      children.add(
        TextFieldWidget(
          controller: mobileController,
          hintText: 'loginPhonePlaceholder'.tr(),
          textInputType: TextInputType.phone,
          maxLength: 11,
          counterText: '',
          validator: (value) => Validators.validateMobile(value),
          prefixIcon: SvgPicture.asset(
            Assets.mobileIcon,
            fit: BoxFit.scaleDown,
            width: 18.gw,
            height: 18.gh,
          ),
        ),
      );
    }

    // Add divider if needed
    if (readOnlyMobile) {
      children.add(Divider(color: Colors.grey[200]));
    } else {
      children.add(15.verticalSpace);
    }

    // Add verification code field and button
    children.add(
      Container(
        padding: EdgeInsets.zero,
        child: Row(
          children: [
            Expanded(
              flex: 3,
              child: SizedBox(
                child: TextFieldWidget(
                  controller: codeController,
                  hintText: 'loginVerificationCodePlaceholder'.tr(),
                  textInputType: TextInputType.text,
                  contentPadding: EdgeInsets.zero,
                  prefixIcon: SvgPicture.asset(
                    Assets.shiledIcon,
                    fit: BoxFit.scaleDown,
                    width: 18.gw,
                    height: 18.gh,
                  ),
                ),
              ),
            ),
            8.horizontalSpace,
            Expanded(
              flex: 2,
              child: SizedBox(
                height: 43.gh,
                child: AnimationConfiguration.synchronized(
                  duration: const Duration(milliseconds: 300),
                  child: ScaleAnimation(
                    scale: 0.95,
                    child: CommonButton(
                      height: 40.gw,
                      title: otpState.isTimerActive
                          ? '${'resend'.tr()} (${otpState.timerDuration}s)'
                          : 'loginVerificationCode'.tr(),
                      fontSize: 14.gsp,
                      onPressed: otpState.isTimerActive ? null : onSendCode,
                      enable: !otpState.isTimerActive,
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );

    return AnimationLimiter(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: AnimationConfiguration.toStaggeredList(
          duration: const Duration(milliseconds: 400),
          childAnimationBuilder: (widget) => SlideAnimation(
            horizontalOffset: 30.0,
            child: FadeInAnimation(
              child: widget,
            ),
          ),
          children: children,
        ),
      ),
    );
  }
}
