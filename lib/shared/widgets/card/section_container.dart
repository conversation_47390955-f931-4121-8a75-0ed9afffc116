import 'package:flutter/material.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';

import '../../theme/font_pallette.dart';
import '../../theme/my_color_scheme.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/shared/app/extension/color_extension.dart';

class SectionContainer extends StatelessWidget {
  final String title;
  final Widget? suffixIcon;
  final Widget child;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;
  final double? borderRadius;
  final Color? backgroundColor;
  final bool showDivider;
  final bool showTitleBar;
  final Function()? onTap;

  const SectionContainer({
    super.key,
    required this.title,
    required this.child,
    this.suffixIcon,
    this.padding,
    this.margin,
    this.borderRadius,
    this.backgroundColor,
    this.showDivider = true,
    this.showTitleBar = true,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: backgroundColor ?? context.theme.cardColor,
        borderRadius: BorderRadius.circular(borderRadius ?? 8.gr),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withNewOpacity(0.05),
            blurRadius: 10.gw,
            spreadRadius: 2.gw,
          ),
        ],
      ),
      child: Padding(
        padding: padding ?? EdgeInsets.all(12.gr),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (showTitleBar) ...[
              GestureDetector(
                onTap: onTap,
                child: Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(8.gr),
                      topRight: Radius.circular(8.gr),
                    ),
                    color: context.theme.cardColor,
                  ),
                  width: double.infinity,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Row(
                        children: [
                          Text(
                            title,
                            style: FontPalette.semiBold12.copyWith(
                              color: context.colorTheme.textPrimary,
                            ),
                          ),
                        ],
                      ),
                      if (suffixIcon != null) suffixIcon!,
                    ],
                  ),
                ),
              )
            ],
            if (showDivider)
              Divider(
                color: context.theme.dividerColor,
                thickness: 0.5.gh,
                height: 0.5.gh,
              ),
            child,
          ],
        ),
      ),
    );
  }
}
