import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/log.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';

import '../../theme/color_pallette.dart';
import '../../theme/font_pallette.dart';
import '../shimmer/shimmer_widget.dart';

class CustomMaterialButton extends StatelessWidget {
  const CustomMaterialButton({
    required this.onPressed,
    this.buttonText = 'Submit',
    this.textStyle,
    this.child,
    this.isOutLined = false,
    this.leading,
    this.height = 42,
    this.width = double.infinity,
    this.fontSize,
    this.textColor,
    this.color,
    this.minWidth = double.infinity,
    this.padding,
    super.key,
    this.fontWeight = FontWeight.w500,
    this.borderRadius = 40,
    this.borderColor,
    this.elevation = 0,
    this.margin,
    this.loadingColor = const Color(0xFF3A5BCD),
    this.isLoading = false,
    this.shimmer = false,
    this.shrinkWrap = false,
    this.isEnabled = true,
    this.clickSpaceTime,
  });

  final Widget? child;
  final String buttonText;
  final Widget? leading;
  final double? fontSize;
  final Color? textColor;
  final Color? color;
  final double? minWidth, width;
  final bool isOutLined;
  final double height;
  final Function()? onPressed;
  final EdgeInsetsGeometry? padding;
  final TextStyle? textStyle;
  final FontWeight? fontWeight;
  final double? borderRadius;
  final Color? borderColor;
  final double? elevation;
  final EdgeInsets? margin;
  final bool isLoading;
  final Color? loadingColor;
  final bool shimmer;
  final bool shrinkWrap;
  final bool isEnabled;

  /// 延时时间 单位秒 默认两秒
  final int? clickSpaceTime;

  TextStyle _getTextStyle() {
    // Start with base style from FontPalette or textStyle parameter
    final baseStyle = textStyle ?? FontPalette.medium16;

    // Create new style with all specified parameters
    return baseStyle.copyWith(
      fontSize: fontSize?.gw ?? baseStyle.fontSize,
      fontWeight: fontWeight ?? baseStyle.fontWeight,
      color: isOutLined ? textColor ?? ColorPalette.primaryColor : ColorPalette.backgroundColor,
    );
  }

  Widget _buildButtonContent() {
    if (isLoading) {
      return Center(
        child: SizedBox(
          height: (height / 2).gh,
          width: (height / 2).gh,
          child: CircularProgressIndicator(
            strokeWidth: 1.75,
            color: loadingColor,
          ),
        ),
      );
    }

    if (child != null) {
      return Center(child: child);
    }

    return Center(
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          if (leading != null) ...[
            leading!,
            5.horizontalSpace,
          ],
          Flexible(
            child: Text(
              buttonText,
              overflow: TextOverflow.ellipsis,
              style: _getTextStyle(),
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    bool bCanPress = true;
    int time = 0;
    if (clickSpaceTime == null) {
      time = 2000;
    } else {
      time = (clickSpaceTime ?? 0) * 1000;
    }

    final buttonWidget = MaterialButton(
      materialTapTargetSize: shrinkWrap ? MaterialTapTargetSize.shrinkWrap : MaterialTapTargetSize.padded,
      height: height.gh,
      minWidth: minWidth,
      onPressed: isEnabled && !isLoading
          ? () {
              if (bCanPress) {
                bCanPress = false;
                onPressed?.call();
                Future.delayed(Duration(milliseconds: time), () {
                  bCanPress = true;
                });
              } else {
                if (kDebugMode) {
                  logDev('✌️按钮重复点击/Repeated button clicks', "CustomMaterialButton.onPressed");
                }
              }
            }
          : null,
      color: isOutLined ? null : (color ?? ColorPalette.primaryColor),
      disabledColor: isOutLined ? null : (color ?? ColorPalette.primaryColor).withNewOpacity(0.5),
      splashColor: color ?? ColorPalette.primaryColor,
      elevation: elevation,
      padding: padding,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(borderRadius?.gr ?? 40.gr),
        side: BorderSide(
          color: !isEnabled && !isOutLined ? Colors.transparent : borderColor ?? ColorPalette.primaryColor,
        ),
      ),
      child: SizedBox(
        height: height.gh,
        width: width,
        child: _buildButtonContent(),
      ),
    );

    final paddedButton = Padding(
      padding: margin ?? EdgeInsets.zero,
      child: buttonWidget,
    );

    return shimmer ? ShimmerWidget(child: paddedButton) : paddedButton;
  }
}
