import os
import codecs
import sys

# 定义需要插入的规则：关键词 -> 要插入的 import
INSERT_RULES = {
    "theme": {
        "keywords": ["context.theme", "context.textTheme", "context.colorTheme"],
        "import_line": "import 'package:gp_stock_app/core/theme/themes.dart';"
    },
    "color_opacity": {
        "keywords": ["withNewOpacity"],
        "import_line": "import 'package:gp_stock_app/shared/app/extension/color_extension.dart';"
    }
}

def process_file(file_path):
    with codecs.open(file_path, 'r', encoding='utf-8') as f:
        lines = f.readlines()
        content = ''.join(lines)

    insert_lines = []

    for rule in INSERT_RULES.values():
        # 如果命中关键词且未包含目标 import，则准备插入
        if any(kw in content for kw in rule["keywords"]) and rule["import_line"] not in content:
            insert_lines.append(rule["import_line"])

    if not insert_lines:
        return  # 没有需要插入的内容

    # 找 import 最后一行后的位置
    last_import_index = -1
    for i, line in enumerate(lines):
        if line.strip().startswith("import "):
            last_import_index = i

    insert_index = last_import_index + 1
    for line in reversed(insert_lines):  # 逆序插入，避免顺序被打乱
        lines.insert(insert_index, line + '\n')

    with codecs.open(file_path, 'w', encoding='utf-8') as f:
        f.writelines(lines)

    print(f"✅ Updated: {file_path}")

def traverse_lib(lib_path):
    for root, dirs, files in os.walk(lib_path):
        for file in files:
            if file.endswith('.dart'):
                full_path = os.path.join(root, file)
                process_file(full_path)

if __name__ == '__main__':
    if len(sys.argv) != 2:
        print("❗️请提供 lib 路径，例如： python3 inject_import.py ./lib")
        sys.exit(1)

    lib_dir = sys.argv[1]
    if not os.path.exists(lib_dir):
        print(f"❌ 路径不存在: {lib_dir}")
        sys.exit(1)

    traverse_lib(lib_dir)
