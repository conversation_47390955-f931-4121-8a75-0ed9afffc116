name: gp_stock_app
description: "A new Flutter project."

publish_to: "none"

version: 1.2.0+2

environment:
  sdk: ^3.6.0

dependencies:
  flutter:
    sdk: flutter
  ## 状态管理
  bloc: ^8.1.4                         # 状态管理库
  flutter_bloc: ^8.1.6                 # Flutter的Bloc状态管理
  hydrated_bloc: ^9.1.5                # Bloc状态持久化
  equatable: ^2.0.7                    # 对象相等性比较
  rxdart: ^0.27.7                      # 响应式编程
  ## 依赖注入
  get_it: ^7.2.0                       # 依赖注入
  injectable: ^2.5.0                   # 依赖注入管理
  ## 网络请求
  dio: ^5.5.1                          # HTTP网络请求
  dio_smart_retry: ^7.0.1              # Dio请求重试
  web_socket_channel: ^3.0.2           # WebSocket通信
  ## 数据处理
  freezed_annotation: ^2.4.4           # 数据类生成
  json_annotation: ^4.9.0              # JSON序列化
  collection: ^1.19.0                  # 集合操作工具
  decimal: ^3.0.2                      # 高精度小数
  encrypt: ^5.0.3                      # 数据加密
  meta: ^1.16.0                        # dart2json
  ## UI组件
  marquee: ^2.3.0                      # 文字滚动效果
  shimmer: ^3.0.0                      # 加载占位动画
  pinput: ^5.0.1                       # 验证码输入框
  fluttertoast: ^8.2.10                # 轻量提示消息
  auto_size_text: ^3.0.0               # 自动调整文字
  carousel_slider: ^5.0.0              # 轮播图组件
  dropdown_search: ^6.0.1              # 下拉搜索框
  pull_to_refresh: ^2.0.0              # 下拉刷新组件
  keyboard_actions: ^4.2.0             # 键盘操作增强
  flutter_bounceable: ^1.1.0           # 弹性点击动画
  flutter_easyloading: ^3.0.5          # 加载动画组件
  animated_flip_counter: ^0.3.4        # 动画数字翻转计数器
  flutter_staggered_animations: ^1.1.1 # 交错动画效果
  ## 图表
  fl_chart: ^0.70.2                    # 图表绘制库
  candlesticks: ^2.1.0                 # K线图表展示
  k_chart_plus:                        # 自定义K线图表
    path: packages/k_chart_plus-1.0.2
  ## 素材与资源
  qr_flutter: ^4.1.0                   # 二维码生成
  flutter_svg: ^2.0.16                 # SVG图片支持
  image_picker: ^1.1.2                 # 图片选择器
  video_player: ^2.9.3                 # 视频播放器
  country_flags: ^3.2.0                # 国家旗帜图标
  image_cropper: ^8.1.0                # 图片裁剪工具
  flutter_lucide: ^1.6.1               # Lucide图标库
  cached_network_image: ^3.3.1         # 缓存网络图片
  ## 文件与存储
  path_provider: ^2.1.5                # 文件路径管理
  flutter_secure_storage: ^9.2.4       # 安全存储数据
  ## 国际化
  intl: ^0.19.0                        # 国际化格式化
  timezone: ^0.10.0                    # 时区处理
  easy_localization: ^3.0.7            # 多语言支持
  ## 其他
  html: ^0.15.4                        # HTML解析
  flutter_html: ^3.0.0-beta.2          # HTML渲染
  url_launcher: ^6.3.1                 # 打开外部链接
  package_info_plus: ^8.3.0            # 应用信息获取
  tencent_cloud_chat_uikit:            # 腾讯云聊天UI
    path: packages/tencent/tencent_cloud_chat_uikit-3.1.0+1

dev_dependencies:
  flutter_test:
    sdk: flutter
  freezed: ^2.5.7
  build_runner: ^2.4.14
  flutter_lints: ^5.0.0
  injectable_generator: ^2.6.2
  json_serializable: ^6.9.0
  change_app_package_name: ^1.5.0
  flutter_launcher_icons: ^0.14.2
  flutter_flavorizr: ^2.2.1
  captcha_plugin_flutter: ^1.1.9 #网易验证码


flavorizr:
  app:
    android:
      flavorDimensions: "flavor-type"
    ios: {}

  flavors:
    pre:
      app:
        name: "GP Pre"
      android:
        applicationId: "com.gp.pre.stock"
        icon: "assets/flavors/pre/ic_launcher.png"
      ios:
        bundleId: "com.gp.pre.stock"
        icon: "assets/flavors/pre/ic_launcher.png"

    gp:
      app:
        name: "GP Stock"
      android:
        applicationId: "com.gp.stock"
        icon: "assets/flavors/gp/ic_launcher.png"
      ios:
        bundleId: "com.gp.stock"
        icon: "assets/flavors/gp/ic_launcher.png"

    rsyp:
      app:
        name: "荣顺优配"
      android:
        applicationId: "com.rsyp.stock"
        icon: "assets/flavors/rsyp/ic_launcher.png"
      ios:
        bundleId: "com.rsyp.stock"
        icon: "assets/flavors/rsyp/ic_launcher.png"

    yhxt:
      app:
        name: "沅和信投"
      android:
        applicationId: "com.yhxt.stock"
        icon: "assets/flavors/yhxt/ic_launcher.png"
      ios:
        bundleId: "com.yhxt.stock"
        icon: "assets/flavors/yhxt/ic_launcher.png"

flutter:
  uses-material-design: true
  assets:
    - assets/images/
    - assets/icons/
    - assets/svg/
    - assets/translations/
    - assets/icons/vip/
    - assets/images/avatars/
    - assets/video/
  fonts:

    - family: Akzidenz-Grotesk
      fonts:
        - asset: assets/fonts/Akzidenz Grotesk-Extra Bold Cond Alt.otf
    - family: Impact
      fonts:
        - asset: assets/fonts/impact-numbers.ttf
